<?php

namespace App\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;
use Twig\TwigFunction;

class MaterialTypeExtension extends AbstractExtension
{
    private const MATERIAL_TYPE_MAPPING = [
        'FERT' => 'FINISHED PRODUCT - Saleable item',
        'HALB' => 'SEMI FINISHED PRODUCT - Component, assemblies',
        'ROH' => 'RAW MATERIAL - New material specification',
        'VERP' => 'PACKAGING - Packaging item'
    ];

    // Mapping étendu pour les cas spéciaux de ROH
    private const EXTENDED_MATERIAL_TYPE_MAPPING = [
        'FERT' => 'FINISHED PRODUCT - Saleable item',
        'HALB' => 'SEMI FINISHED PRODUCT - Component, assemblies, BoM etc...',
        'ROH' => 'RAW MATERIAL - New material specification',
        'ROH_LITERATURE' => 'LITTERATURE - Document',
        'ROH_NON_VALUATED' => 'NON VALUATED MATERIAL - Cable, customer action etc...',
        'VERP' => 'PACKAGING - Packaging item such as cardboard box, etc...'
    ];

    private const LEGACY_MAPPING = [
        'FINISHED PRODUCT' => 'FERT',
        'SEMI-FINISHED PRODUCT' => 'HALB',
        'SEMI FINISHED PRODUCT' => 'HALB',
        'RAW MATERIAL' => 'ROH',
        'PACKAGING' => 'VERP',
        'LITTERATURE' => 'ROH',
        'LITERATURE' => 'ROH',
        'NON VALUATED MATERIAL' => 'ROH'
    ];

    public function getFilters(): array
    {
        return [
            new TwigFilter('material_type_label', [$this, 'getMaterialTypeLabel']),
            new TwigFilter('material_type_sap', [$this, 'getMaterialTypeSAP']),
        ];
    }

    public function getFunctions(): array
    {
        return [
            new TwigFunction('material_type_options', [$this, 'getMaterialTypeOptions']),
            new TwigFunction('all_material_types', [$this, 'getAllMaterialTypes']),
        ];
    }

    /**
     * Convertit un code SAP en libellé descriptif
     */
    public function getMaterialTypeLabel(?string $sapCode): string
    {
        if (!$sapCode) {
            return '';
        }

        return self::MATERIAL_TYPE_MAPPING[$sapCode] ?? $sapCode;
    }

    /**
     * Convertit un libellé en code SAP
     */
    public function getMaterialTypeSAP(?string $label): string
    {
        if (!$label) {
            return '';
        }

        // Si c'est déjà un code SAP valide, le retourner
        if (array_key_exists($label, self::MATERIAL_TYPE_MAPPING)) {
            return $label;
        }

        // Sinon, essayer de mapper depuis les anciens libellés
        return self::LEGACY_MAPPING[$label] ?? $label;
    }

    /**
     * Retourne les options HTML pour un select de type de matériau
     */
    public function getMaterialTypeOptions(?string $selectedValue = null): string
    {
        $options = [];

        // Options principales avec leurs descriptions
        $optionsData = [
            ['code' => 'FERT', 'label' => 'FINISHED PRODUCT - Saleable item'],
            ['code' => 'HALB', 'label' => 'SEMI FINISHED PRODUCT - Component, assemblies'],
            ['code' => 'ROH', 'label' => 'LITTERATURE - Document'],
            ['code' => 'ROH', 'label' => 'NON VALUATED MATERIAL - Cable, customer action'],
            ['code' => 'VERP', 'label' => 'PACKAGING - Packaging item'],
            ['code' => 'ROH', 'label' => 'RAW MATERIAL - New material specification']
        ];

        foreach ($optionsData as $option) {
            $selected = ($selectedValue === $option['code']) ? ' selected' : '';
            $options[] = sprintf(
                '<option value="%s"%s>%s</option>',
                htmlspecialchars($option['code']),
                $selected,
                htmlspecialchars($option['label'])
            );
        }

        return implode("\n", $options);
    }

    /**
     * Retourne tous les types de matériaux avec leurs mappings
     */
    public function getAllMaterialTypes(): array
    {
        return [
            'mapping' => self::MATERIAL_TYPE_MAPPING,
            'legacy' => self::LEGACY_MAPPING,
            'options' => [
                ['code' => 'FERT', 'label' => 'FINISHED PRODUCT - Saleable item'],
                ['code' => 'HALB', 'label' => 'SEMI FINISHED PRODUCT - Component, assemblies'],
                ['code' => 'ROH', 'label' => 'LITTERATURE - Document'],
                ['code' => 'ROH', 'label' => 'NON VALUATED MATERIAL - Cable, customer action'],
                ['code' => 'VERP', 'label' => 'PACKAGING - Packaging item'],
                ['code' => 'ROH', 'label' => 'RAW MATERIAL - New material specification']
            ]
        ];
    }
}
