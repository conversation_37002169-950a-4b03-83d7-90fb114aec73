<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250617061702 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE product_code DROP family, DROP subfamily, DROP type, DROP subtype, DROP category, DROP subcategory, DROP legacy_id');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE product_code ADD family VARCHAR(255) DEFAULT NULL, ADD subfamily VARCHAR(255) DEFAULT NULL, ADD type VARCHAR(255) DEFAULT NULL, ADD subtype VARCHAR(255) DEFAULT NULL, ADD category VARCHAR(255) DEFAULT NULL, ADD subcategory VARCHAR(255) DEFAULT NULL, ADD legacy_id INT DEFAULT NULL');
    }
}
