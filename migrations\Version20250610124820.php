<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250610124820 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE document ADD qual_owner_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE document ADD CONSTRAINT FK_D8698A76388B8856 FOREIGN KEY (qual_owner_id) REFERENCES user (id)');
        $this->addSql('CREATE INDEX IDX_D8698A76388B8856 ON document (qual_owner_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE document DROP FOREIGN KEY FK_D8698A76388B8856');
        $this->addSql('DROP INDEX IDX_D8698A76388B8856 ON document');
        $this->addSql('ALTER TABLE document DROP qual_owner_id');
    }
}
