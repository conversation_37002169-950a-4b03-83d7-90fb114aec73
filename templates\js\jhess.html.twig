<script>
    // Fonctions pour afficher/masquer l'indicateur de chargement
    function showLoading() {
        $('#loadingOverlay').css('display', 'flex');
    }

    function hideLoading() {
        $('#loadingOverlay').css('display', 'none');
    }

    function validateField(input, regex, documentId, field, rulesMessage) {
        console.log('validateField:', input, regex, documentId, field, rulesMessage);
        const value = input.val().trim();
        if (!regex.test(value)) {
            input.addClass('is-invalid');
            Toast.fire({
                icon: 'error',
                title: `Le champ "${field}" n'est pas valide.`,
                text: `Règle : ${rulesMessage}`
            });
            return;
        }
        input.removeClass('is-invalid');
        updateDocumentField(documentId, field, value);
    }

    // Tri au clic sur l'entête
    function sortTable() {
        var table = $('#table');
        var th = $(this);
        var index = th.index();
        var tbody = table.children('tbody');

        var tr = tbody.children('tr').toArray().sort(function(a, b){
            var aText = $(a).find('td').eq(index).text();
            var bText = $(b).find('td').eq(index).text();

            var aInt = parseInt(aText, 10);
            var bInt = parseInt(bText, 10);

            if (!isNaN(aInt) && !isNaN(bInt)) {
                return aInt - bInt;
            }

            if ($(a).find('td').eq(index).attr('date')) {
                aText = $(a).find('td').eq(index).attr('date');
            }
            if ($(b).find('td').eq(index).attr('date')) {
                bText = $(b).find('td').eq(index).attr('date');
            }

            if ($(a).find('td').eq(index).find('.badge[week]').length) {
                aText = $(a).find('td').eq(index).find('.badge[week]').attr('week').padStart(2, '0');
            }
            if ($(b).find('td').eq(index).find('.badge[week]').length) {
                bText = $(b).find('td').eq(index).find('.badge[week]').attr('week').padStart(2, '0');
            }

            return aText.localeCompare(bText);
        });

        if(th.hasClass('sort-asc')) {
            th.removeClass('sort-asc');
            th.addClass('sort-desc');
            tr = tr.reverse();
            $('th').find('i').remove();
            th.append('<i class="fas fa-sort-up"></i>');
        } else {
            th.removeClass('sort-desc');
            th.addClass('sort-asc');
            $('th').find('i').remove();
            th.append('<i class="fas fa-sort-down"></i>');
        }
        tbody.empty();
        tbody.append(tr);
    }

    function refreshTable(page = 1) {
        showLoading(); // Afficher l'indicateur de chargement

        // Construire l'URL avec le paramètre de page et mettre à jour l'URL dans la barre d'adresse
        let url = new URL(window.location.href);
        url.searchParams.set('page', page);
        history.pushState(null, '', url.toString());

        $.ajax({
            url: url.toString(),
            type: 'GET',
            success: function(data) {
                // Extraire et remplacer le contenu du tableau
                let newTableContainer = $(data).find('#table-container');
                $('#table-container').html(newTableContainer.html());

                // Extraire et remplacer le contenu de la pagination
                let newPaginationContainer = $(data).find('.pagination-container');
                $('.pagination-container').html(newPaginationContainer.html());

                // Réinitialiser les événements sur les contrôles de pagination
                initPaginationEvents();

                // Appels complémentaires si besoin
                fetchDatalist("{{ path('get_productCode') }}", "#productCode");
                fetchDatalist("{{ path('get_hts') }}", "#hts");
                fetchDatalist("{{ path('get_eccn') }}", "#eccn");
                fetchDatalist("{{ path('get_purchasingGroup') }}", "#purchasingGroup");
                fetchDatalist("{{ path('get_commodityCode') }}", "#commodityCode");
                fetchDatalist("{{ path('get_prisDans1') }}", "#prisDans1");
                fetchDatalist("{{ path('get_prisDans2') }}", "#prisDans2");

                hideLoading(); // Masquer l'indicateur de chargement
            },
            error: function(error) {
                console.error('Erreur lors du rafraîchissement du tableau:', error);
                hideLoading(); // Masquer l'indicateur de chargement en cas d'erreur
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors du rafraîchissement du tableau'
                });
            }
        });
    }


    // Fonction pour initialiser les événements sur les contrôles de pagination
    function initPaginationEvents() {
        $('.pagination-container .page-link').on('click', function(e) {
            e.preventDefault();
            let href = $(this).attr('href');
            if (href) {
                let url = new URL(href, window.location.origin);
                let page = url.searchParams.get('page') || 1;
                refreshTable(page);
            }
        });
    }


    function filterTableByColumn() {
        $('#table tbody tr').each(function() {
            var row = $(this);
            var tds = row.children('td'); // uniquement les td de premier niveau (pas ceux imbriqués)
            var showRow = tds.toArray().every((td, index) => {
                var inputSearch = $('input[data-col="' + index + '"]');
                if (!inputSearch.length) return true;

                var searchVal = inputSearch.val().toLowerCase().trim();
                if (!searchVal) return true;

                var cellText = $(td).text().toLowerCase().trim();
                var extraTexts = [];

                // Récupération du texte des badges
                $(td).find('.badge').each(function() {
                    extraTexts.push($(this).text().toLowerCase());
                });

                // Récupération du texte de l'attribut 'txt' (tooltip)
                var tooltipText = $(td).find('[txt]').attr('txt') || '';
                if (tooltipText) extraTexts.push(tooltipText.toLowerCase());

                // Récupération de la valeur d'un input ou select dans la cellule
                var cellInput = $(td).find('input, select');
                if (cellInput.length) {
                    return (cellInput.val().toLowerCase().trim()).includes(searchVal);
                }

                return (cellText + ' ' + extraTexts.join(' ')).includes(searchVal);
            });

            row.toggle(showRow);
        });
    }



    $(document).on('click', '#entetes th', sortTable);

    $(document).on('keyup', 'thead input', function() {
        filterTableByColumn();
    });

    let debounceTimers = {};
    let docTypeDisplayName = {
        'ASSY': 'Assemblage',
        'MACH': 'Usinage',
        'MOLD': 'Moulage',
        'DOC':  'Document',
        'PUR':  'Achat'
    };

    function updateDocumentField(documentId, field, value) {
        if (debounceTimers[documentId + field]) {
            clearTimeout(debounceTimers[documentId + field]);
        }
        debounceTimers[documentId + field] = setTimeout(() => {
            if (field !== 'doctype') {
                sendUpdateRequest(documentId, field, value);
            } else {
                Swal.fire({
                    title: 'Êtes-vous sûr ?',
                    text: "Le document arrivera dans le département " + docTypeDisplayName[value] + ".",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Oui, changer !'
                }).then((result) => {
                    if (result.isConfirmed) {
                        sendUpdateRequest(documentId, field, value);
                    }
                });
            }
        }, 300);
    }

    function sendUpdateRequest(documentId, field, value) {
        if (!documentId) {
            return;
        }
        showLoading(); // Afficher l'indicateur de chargement
        $.ajax({
            url: "{{ path('update_document') }}",
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ id: documentId, field: field, value: value }),
            success: function(data) {
                hideLoading(); // Masquer l'indicateur de chargement
                if (data.status === 'success') {
                    console.log('Mise à jour réussie pour:', field);
                    Toast.fire({
                        icon: 'success',
                        title: 'Mise à jour réussie'
                    });
                } else {
                    Toast.fire({
                        icon: 'error',
                        title: 'Erreur: ' + data.message
                    });
                }
                fetchDatalist("{{ path('get_productCode') }}", "#productCode");
                fetchDatalist("{{ path('get_hts') }}", "#hts");
                fetchDatalist("{{ path('get_eccn') }}", "#eccn");
                fetchDatalist("{{ path('get_purchasingGroup') }}", "#purchasingGroup");
                fetchDatalist("{{ path('get_commodityCode') }}", "#commodityCode");
                fetchDatalist("{{ path('get_prisDans1') }}", "#prisDans1");
                fetchDatalist("{{ path('get_prisDans2') }}", "#prisDans2");
                // productCode();

            },
            error: function(error) {
                hideLoading(); // Masquer l'indicateur de chargement en cas d'erreur
                console.error('Erreur lors de la mise à jour:', error);
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors de la mise à jour'
                });
            }
        });
    }


    $(document).on('change', '.metro-control-checkbox', function() {
        var documentId = $(this).data('id');
        var metroControl = [];

        $('.metro-control-checkbox[data-id="' + documentId + '"]:checked').each(function() {
            metroControl.push($(this).val());
        });

        $.ajax({
            url: '{{ path('update_metro_control') }}', // Chemin Twig
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify({
                id: documentId,
                metroControl: metroControl
            }),
            success: function(response) {
                if (response.status === 'success') {
                    Toast.fire({
                        icon: 'success',
                        title: 'Mise à jour réussie'
                    });
                } else {
                    alert('Erreur: ' + response.message);
                }
            },
            error: function() {
                alert('Erreur lors de la mise à jour de MetroControl');
            }
        });
    });

    function signSelectedDocumentsVisa() {
        // Récupérer toutes les checkboxes cochées
        const checkboxes = document.querySelectorAll('.doc-select:checked');
        console.log('data-current-steps:', checkboxes);

        // S'assurer qu'il y a au moins un document sélectionné
        if (checkboxes.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'Aucune sélection',
                text: 'Veuillez cocher au moins un document à signer.'
            });
            return;
        }

        Swal.fire({
            title: 'Êtes-vous sûr ?',
            text: "Vous êtes sur le point de signer les documents sélectionnés.",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, signer !',
            cancelButtonText: 'Annuler',
            customClass: {
                confirmButton: 'btn btn-sm btn-success',
                cancelButton: 'btn btn-sm btn-danger'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                checkboxes.forEach(checkbox => {
                    // Récupérer l’ID et les currentsteps
                    const docId = checkbox.dataset.documentId;
                    const steps = JSON.parse(checkbox.dataset.currentSteps);
                    console.log(steps);
                    // Appel direct à votre fonction existante

                    createVisa_inventory_multi(docId, steps);
                });
            }
            // Option : rafraîchir la table au bout d’un petit délai
            setTimeout(() => {
                refreshTable();
            }, 1000);
        });

    }


    function createVisa_inventory_multi(documentId) {

        const Visadata = {
            documentId: documentId,
        };

        $.ajax({
            url: "{{ path('create_visa_inventory') }}",
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify(Visadata),
            success: function(result) {
                if (result.status === "success") {
                    Toast.fire({
                        icon: "success",
                        title: result.message
                    });
                    refreshTable();
                } else {
                    Toast.fire({
                        icon: "error",
                        title: "Erreur lors de la création du visa",
                        text: result.message
                    });
                }
            },
            error: function(xhr) {
                // Extraire le message d'erreur du backend
                const errorResponse = xhr.responseJSON;

                if (errorResponse && errorResponse.message === "visa valid existant") {
                    Toast.fire({
                        icon: "warning",
                        title: "Visa déjà créé",
                        text: "Un visa pour ce document existe déjà."
                    });
                } else if (errorResponse && errorResponse.message) {
                    Toast.fire({
                        icon: "error",
                        title: "Erreur",
                        text: errorResponse.message
                    });
                } else {
                    Toast.fire({
                        icon: "error",
                        title: "Une erreur s'est produite",
                        text: "Veuillez vérifier les données ou réessayer plus tard."
                    });
                }
            }
        });
    }

    function createVisa_inventory(documentId) {

        const Visadata = {
            documentId: documentId,
        };
        Swal.fire({
                title: 'Êtes-vous sûr ?',
                text: "Vous êtes sur le point de créer un visa.",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Oui, créer !',
                cancelButtonText: 'Annuler',
                customClass: {
                    confirmButton: 'btn btn-sm btn-success',
                    cancelButton: 'btn btn-sm btn-danger'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                showLoading(); // Afficher l'indicateur de chargement
                $.ajax({
                    url: "{{ path('create_visa_inventory') }}",
                    type: "POST",
                    contentType: "application/json",
                    data: JSON.stringify(Visadata),
                    success: function(result) {
                        hideLoading(); // Masquer l'indicateur de chargement
                        if (result.status === "success") {
                            Toast.fire({
                                icon: "success",
                                title: result.message
                            });
                            refreshTable();
                        } else {
                            Toast.fire({
                                icon: "error",
                                title: "Erreur lors de la création du visa",
                                text: result.message
                            });
                        }
                    },
                    error: function(xhr) {
                        hideLoading(); // Masquer l'indicateur de chargement en cas d'erreur
                        // Extraire le message d'erreur du backend
                        const errorResponse = xhr.responseJSON;

                        if (errorResponse && errorResponse.message === "visa valid existant") {
                            Toast.fire({
                                icon: "warning",
                                title: "Visa déjà créé",
                                text: "Un visa pour ce document existe déjà."
                            });
                        } else if (errorResponse && errorResponse.message) {
                            Toast.fire({
                                icon: "error",
                                title: "Erreur",
                                text: errorResponse.message
                            });
                        } else {
                            Toast.fire({
                                icon: "error",
                                title: "Une erreur s'est produite",
                                text: "Veuillez vérifier les données ou réessayer plus tard."
                            });
                        }
                    }
                });
            }
        });
    }



    function signSelectedDocuments() {
        // Récupérer toutes les checkboxes cochées
        const checkboxes = document.querySelectorAll('.doc-select:checked');
        console.log('data-current-steps:', checkboxes);

        // S'assurer qu'il y a au moins un document sélectionné
        if (checkboxes.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'Aucune sélection',
                text: 'Veuillez cocher au moins un document à signer.'
            });
            return;
        }

        // Appeler createVisa_inventory pour chacun
        // ask swal2 for confirmation before signing
        Swal.fire({
            title: 'Êtes-vous sûr ?',
            text: "Vous êtes sur le point de signer les documents sélectionnés.",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, signer !',
            cancelButtonText: 'Annuler',
            customClass: {
                confirmButton: 'btn btn-sm btn-success',
                cancelButton: 'btn btn-sm btn-danger'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                checkboxes.forEach(checkbox => {
                    // Récupérer l’ID et les currentsteps
                    const docId = checkbox.dataset.documentId;
                    const steps = JSON.parse(checkbox.dataset.currentSteps);
                    console.log(steps);
                    // Appel direct à votre fonction existante

                    createVisa_multi(docId);
                });
            }
            setTimeout(() => {
                refreshTable();
            }, 1000);
        });


        // Option : rafraîchir la table au bout d’un petit délai

    }

    function createVisa_multi(documentId) {
        const data = {
            documentId: documentId,
            name: window.location.pathname.split('/').pop()
        };
        $.ajax({
            url: "{{ path('create_visa') }}",
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify(data),
            success: function(result) {
                if (result.status === "success") {
                    Toast.fire({
                        icon: "success",
                        title: result.message
                    });
                    refreshTable();
                } else {
                    Toast.fire({
                        icon: "error",
                        title: "Erreur lors de la création du visa",
                        text: result.message
                    });
                }
            },
            error: function(xhr) {
                // Extraire le message d'erreur du backend
                const errorResponse = xhr.responseJSON;

                if (errorResponse && errorResponse.message === "visa valid existant") {
                    Toast.fire({
                        icon: "warning",
                        title: "Visa déjà créé",
                        text: "Un visa pour ce document existe déjà."
                    });
                } else if (errorResponse && errorResponse.message) {
                    Toast.fire({
                        icon: "error",
                        title: "Erreur",
                        text: errorResponse.message
                    });
                } else {
                    Toast.fire({
                        icon: "error",
                        title: "Une erreur s'est produite",
                        text: "Veuillez vérifier les données ou réessayer plus tard."
                    });
                }
            }
        });
    }

    function createVisa(documentId) {
        const data = {
            documentId: documentId,
            name: window.location.pathname.split('/').pop()
        };
        Swal.fire({
            title: 'Êtes-vous sûr ?',
            text: "Vous êtes sur le point de créer un visa.",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, créer !',
            cancelButtonText: 'Annuler',
            customClass: {
                confirmButton: 'btn btn-sm btn-success',
                cancelButton: 'btn btn-sm btn-danger'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: "{{ path('create_visa') }}",
                    type: "POST",
                    contentType: "application/json",
                    data: JSON.stringify(data),
                    success: function(result) {
                        if (result.status === "success") {
                            Toast.fire({
                                icon: "success",
                                title: result.message
                            });
                            // Recharger complètement la page pour s'assurer que tous les champs sont mis à jour
                            window.location.reload();
                        } else {
                            Toast.fire({
                                icon: "error",
                                title: "Erreur lors de la création du visa",
                                text: result.message
                            });
                        }
                    },
                    error: function(xhr) {
                        // Extraire le message d'erreur du backend
                        const errorResponse = xhr.responseJSON;

                        if (errorResponse && errorResponse.message === "visa valid existant") {
                            Toast.fire({
                                icon: "warning",
                                title: "Visa déjà créé",
                                text: "Un visa pour ce document existe déjà."
                            });
                        } else if (errorResponse && errorResponse.message) {
                            Toast.fire({
                                icon: "error",
                                title: "Erreur",
                                text: errorResponse.message
                            });
                        } else {
                            Toast.fire({
                                icon: "error",
                                title: "Une erreur s'est produite",
                                text: "Veuillez vérifier les données ou réessayer plus tard."
                            });
                        }
                    }
                });
            }
        });
    }


    function showVisas(documentId) {
        showLoading(); // Afficher l'indicateur de chargement
        $.ajax({
            url: '{{ path("document_visas", {"id": "ID_PLACEHOLDER"}) }}'.replace('ID_PLACEHOLDER', documentId),
            type: 'GET',
            success: function(data) {
                hideLoading(); // Masquer l'indicateur de chargement
                let modalBody = document.querySelector('#modalVisas .modal-body');
                modalBody.innerHTML = '';
                if (data.length === 0) {
                    modalBody.innerHTML = '<p>Aucun visa pour ce document.</p>';
                } else {
                    // Trier les visas par ordre chronologique (dateVisa)
                    data.sort((a, b) => {
                        // Convertir les dates en objets Date pour la comparaison
                        const dateA = new Date(a.dateVisa);
                        const dateB = new Date(b.dateVisa);
                        return dateA - dateB; // Tri croissant (plus ancien en premier)
                    });

                    let table = '<table class="table table-sm table-bordered" id="modal-visa">' +
                                '<thead><tr>' +
                                '<th class="text-center">Nom du visa</th>' +
                                '<th class="text-center">Signé par</th>' +
                                '<th class="text-center">Date de signature</th>' +
                                '</tr></thead>' +
                                '<tbody>';
                    data.forEach(visa => {
                        table += '<tr>' +
                                '<td class="text-center">' + visa.name.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ') + '</td>' +
                                '<td class="text-center">' + visa.signer + '</td>' +
                                '<td class="text-center">' + visa.dateVisa + '</td>' +
                                '</tr>';
                    });
                    table += '</tbody></table>';
                    modalBody.innerHTML = table;
                }

                // On ouvre la modale
                let myModal = new bootstrap.Modal(document.getElementById('modalVisas'));
                myModal.show();
            },
            error: function(err) {
                hideLoading(); // Masquer l'indicateur de chargement en cas d'erreur
                Toast.fire({
                    icon: 'error',
                    title: "Erreur lors de la récupération des visas"
                });
            }
        });
    }

    document.title = window.location.pathname.split('/').pop();

    // Variable globale pour stocker les IDs de tous les documents à travers toutes les pages
    let allDocumentsAcrossPages = [];
    let allDocumentsSelected = false;

    function selectAllDocumentsAcrossPages() {
        if (allDocumentsSelected) {
            // Désélectionner tous les documents
            $('.doc-select').prop('checked', false);
            $('.doc-select').closest('tr').find('td').removeClass('selected-row');
            $(document).find('#signer-mass').hide();
            $(document).find('#select-all-pages').text('Sélectionner tous');
            $(document).find('#select-all-pages').removeClass('bg-warning').addClass('bg-info');
            allDocumentsSelected = false;
        } else {
            // Récupérer tous les IDs de documents pour cette place
            const place = window.location.pathname.split('/').pop();
            showLoading();

            $.ajax({
                url: `/document/get-all-ids/${place}`,
                type: 'GET',
                success: function(data) {
                    hideLoading();
                    allDocumentsAcrossPages = data;

                    // Sélectionner tous les documents visibles sur la page actuelle
                    $('.doc-select').prop('checked', true);
                    $('.doc-select').closest('tr').find('td').addClass('selected-row');

                    // Mettre à jour le bouton de signature en masse
                    $(document).find('#signer-mass').show();
                    $(document).find('#signer-mass').text(`Signer tous (${allDocumentsAcrossPages.length})`);

                    // Mettre à jour le bouton de sélection
                    $(document).find('#select-all-pages').text('Désélectionner tous');
                    $(document).find('#select-all-pages').removeClass('bg-info').addClass('bg-warning');

                    allDocumentsSelected = true;

                    Toast.fire({
                        icon: 'success',
                        title: `${allDocumentsAcrossPages.length} documents sélectionnés à travers toutes les pages`
                    });
                },
                error: function(error) {
                    hideLoading();
                    console.error('Erreur lors de la récupération des IDs de documents:', error);
                    Toast.fire({
                        icon: 'error',
                        title: 'Erreur lors de la récupération des documents'
                    });
                }
            });
        }
    }

    $(document).on('click', '.doc-select', function() {
        // Désactiver le mode "tous les documents sélectionnés" si on clique sur une checkbox individuelle
        if (allDocumentsSelected) {
            allDocumentsSelected = false;
            $(document).find('#select-all-pages').text('Sélectionner tous');
            $(document).find('#select-all-pages').removeClass('bg-warning').addClass('bg-info');
        }

        // count it and set
        var count = $('.doc-select:checked').length;
        if ($(this).is(':checked')) {
            $(this).closest('tr').find('td').addClass('selected-row');
        } else {
            $(this).closest('tr').find('td').removeClass('selected-row');
        }
        if (count <= 1) {
            $(document).find('#signer-mass').hide();
        } else {
            $(document).find('#signer-mass').show();
            $(document).find('#signer-mass').text('Signer (' + count + ')');
        }
    });

    function addComment(documentId, inputElement) {
        const content = $(inputElement).val().trim();
        const type = 'secondaire';
        let state = window.location.pathname.split('/').pop();
        switch (state) {
            case 'Qual_Logistique':
            case 'Logistique':
                state = 'Inventaire';
                break;
            case 'Quality':
                state = 'Qualité';
                break;
            case 'Project':
                state = 'Projet';
                break;
            case 'Metro':
                state = 'Métrologie';
                break;
            case 'QProd':
                state = 'Qualité Production';
                break;
            case 'Assembly':
                state = 'Assemblage';
                break;
            case 'Machining':
                state = 'Usinage';
                break;
            case 'Molding':
                state = 'Moulage';
                break;
            case 'Methode_assemblage':
                state = 'Méthode Assemblage';
                break;
            case 'Planning':
                state = 'Logistique';
                break;
            case 'Indus':
                state = 'Gammes Assemblage';
                break;
            case 'methode_Labo':
                state = 'Laboratoire';
                break;
            default:
                break;
            }

        console.log('content:', content, 'type:', type, 'state:', state);

        if (content === '') {
            Swal.fire({
                icon: 'warning',
                title: 'Commentaire vide',
                text: 'Veuillez entrer un commentaire avant de soumettre.'
            });
            return;
        }

        showLoading(); // Afficher l'indicateur de chargement
        $.ajax({
            url: "{{ path('app_commentaire_new') }}",
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ documentId: documentId, content: content, type: type, state: state }),
            success: function(response) {
                hideLoading(); // Masquer l'indicateur de chargement
                Swal.fire({
                    icon: 'success',
                    title: 'Commentaire ajouté',
                    text: response
                });
                $(inputElement).val(''); // Réinitialise le champ de commentaire spécifique
                refreshTable();
            },
            error: function(xhr) {
                hideLoading(); // Masquer l'indicateur de chargement en cas d'erreur
                Swal.fire({
                    icon: 'error',
                    title: 'Erreur',
                    text: 'Une erreur s\'est produite lors de l\'ajout du commentaire.'
                });
            }
        });
    }

    // Attacher un gestionnaire d'événement global pour les champs de commentaires
    $(document).on('change', '.comment-input', function () {
        const documentId = $(this).data('document-id'); // Récupère l'ID du document à partir de l'attribut data
        console.log('documentId:', $(this).data('document-id'));
        addComment(documentId, this); // Passe l'élément actuel comme paramètre
    });



    // Initialisation globale des tooltips sur la page
    $(document).ready(function() {
    $('[data-bs-toggle="tooltip"]').each(function() {
        new bootstrap.Tooltip(this);
    });
    });

        // Fonction pour afficher les commentaires dans le modal
    function displayComments(comments) {
        const modalBody = document.getElementById('commentsModalBody');

        if (!comments || comments.length === 0) {
            modalBody.innerHTML = `
                <div class="no-comments">
                    <i class="fas fa-comment-slash fa-3x mb-3" style="color: #ccc;"></i>
                    <p>Aucun commentaire pour ce document.</p>
                </div>
            `;
            return;
        }

        let html = `<div class="comments-count">
            <i class="fas fa-comments me-2"></i>
            ${comments.length} commentaire${comments.length > 1 ? 's' : ''}
        </div>`;

        comments.forEach(comment => {
            const createdAt = new Date(comment.created_at);
            const formattedDate = createdAt.toLocaleDateString('fr-FR', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });

            html += `
                <div class="comment-item">
                    <div class="comment-header">
                        <span class="comment-state">${comment.state.toUpperCase() || 'Général'}</span>
                        <div class="comment-meta">
                            <i class="fas fa-user me-1"></i>
                            ${comment.user ? comment.user.prenom + ' ' + comment.user.nom : 'Utilisateur inconnu'}
                            <i class="fas fa-clock ms-3 me-1"></i>
                            ${formattedDate}
                        </div>
                    </div>
                    <div class="comment-content">
                        ${comment.commentaire}
                    </div>
                </div>
            `;
        });

        modalBody.innerHTML = html;
    }

    // Affiche/Recharge le tooltip au survol
    function loadCommentsTooltip(iconElem) {
        const documentId = $(iconElem).data('document-id');
        const txt = $(iconElem).attr('txt') || '<em>Aucun commentaire</em>';

        // Récupérer une instance du tooltip s’il existe
        let tooltip = bootstrap.Tooltip.getInstance(iconElem);

        // S’il existe déjà, on le supprime pour réinitialiser
        if (tooltip) {
            tooltip.dispose();
        }

        // On modifie 'title' avec le contenu brut (HTML)
        $(iconElem).attr('title', txt);

        // On recrée le tooltip, en spécifiant qu'on autorise le HTML et qu'on le gère en manuel
        tooltip = new bootstrap.Tooltip(iconElem, {
            html: true,
            trigger: 'manual'
        });

        // On l'affiche
        tooltip.show();

        // Lorsque la souris quitte l'icône, on masque le tooltip
        $(iconElem).on('mouseleave', function() {
            hideCommentsTooltip(iconElem);
        });
    }

    // Masque simplement le tooltip
    function hideCommentsTooltip(iconElem) {
        let tooltip = bootstrap.Tooltip.getInstance(iconElem);
        if (tooltip) {
            tooltip.hide();
            // Optionnel : on retire l'événement pour éviter un cumul d'écouteurs
            $(iconElem).off('mouseleave');
        }
    }



    $(document).on('change', '.matProdType-select', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'matProdType', value);
    });

    $(document).on('change', '.procType-select', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'procType', value);
    });

    $(document).on('change', '.doc-type-select', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'doctype', value);
    });

    $(document).on('change', '.unit-select', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'unit', value);
    });

    $(document).on('change', '.prodAgent-select', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'prodAgent', value);
    });

    $(document).on('change', '.gestionnaire-select', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'superviseur', value);
    });

    $(document).on('change', '.hts-input', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'hts', value);
    });

    $(document).on('change', '.reference-input', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'reference', value);
    });

    $(document).on('change', '.switchAletiq-checkbox', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).is(':checked');
        updateDocumentField(documentId, 'switchAletiq', value);
    });

    $(document).on('change', '.eccn-input', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'eccn', value);
    });

    $(document).on('change', '.purchasingGroup-input', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'purchasingGroup', value);
    });

    $(document).on('change', '.commodityCode-input', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'commodityCode', value);
    });



    $(document).on('change', '.leadtime-input', function() {
        const documentId = $(this).attr('doc-id');
        validateField($(this), /^-?\d+$/, documentId, 'leadtime', 'Doit contenir uniquement des chiffres positifs ou négatifs');
    });

    $(document).on('change', '.cls-input', function() {
        const documentId = $(this).attr('doc-id');
        validateField($(this), /^-?\d+$/, documentId, 'cls', 'Doit contenir uniquement des chiffres positifs ou négatifs');
    });

    $(document).on('change', '.fia-input', function() {
        const documentId = $(this).attr('doc-id');
        validateField($(this), /^[a-zA-Z]+$/, documentId, 'fia', 'Doit contenir uniquement des lettres');
    });

    $(document).on('change', '.moq-input', function() {
        const documentId = $(this).attr('doc-id');
        validateField($(this), /^-?\d+$/, documentId, 'moq', 'Doit contenir uniquement des chiffres positifs ou négatifs');
    });

    $(document).on('change', '.productCode-input', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'productCode', value);
    });

    $(document).on('change', '.prisDans1-input', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'prisDans1', value);
    });

    $(document).on('change', '.prisDans2-input', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'prisDans2', value);
    });

    $(document).on('change', '.metrotime-input', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'metrotime', value);
    });

    $(document).on('change', '.mof-input', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'mof', value);
        setTimeout(() => {
            refreshTable();
        }, 300);
    });

    $(document).on('change', '.inspect-select', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'qInspection', value);
    });

    {# $(document).on('change', '.qInspection-multiple', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'qInspection', value);
    }); #}

    $(document).on('change', '.qDocRec-multiple', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'qDocRec', value);
    });

    $(document).on('change', '.gamme-select', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'qControlrouting', value);
    });

    $(document).on('change', '.dynamisation-select', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'qDynamization', value);
    });

    $(document).on('change', '.qual-owner-select', function() {
        const documentId = $(this).attr('doc-id');
        const value = $(this).val();
        updateDocumentField(documentId, 'qualOwner', value);
    });

    function fetchDatalist(url, selector) {
    $.ajax({
        url: url,
        method: 'GET',
        dataType: 'json',
        success: function (data) {
            const datalist = $(selector);
            datalist.empty();
            const dataArray = Object.values(data);

            dataArray.forEach(function (item) {
                let option;
                // Vérifier si l'item est un objet avec value et label (pour productCode)
                if (typeof item === 'object' && item.value !== undefined && item.label !== undefined) {
                    option = $('<option></option>').val(item.value).text(item.label);
                } else {
                    // Comportement par défaut pour les autres datalists
                    option = $('<option></option>').val(item);
                }
                datalist.append(option);
            });
        },
        error: function (error) {
            console.error(`Error fetching data for selector '${selector}':`, error);
        }
    });
}

$(document).ready(function () {
    fetchDatalist("{{ path('get_productCode') }}", "#productCode");
    fetchDatalist("{{ path('get_hts') }}", "#hts");
    fetchDatalist("{{ path('get_eccn') }}", "#eccn");
    fetchDatalist("{{ path('get_purchasingGroup') }}", "#purchasingGroup");
    fetchDatalist("{{ path('get_commodityCode') }}", "#commodityCode");
    fetchDatalist("{{ path('get_prisDans1') }}", "#prisDans1");
    fetchDatalist("{{ path('get_prisDans2') }}", "#prisDans2");

    // Initialiser les événements de pagination
    initPaginationEvents();
});


function fetchGestionnaire() {
        $.ajax({
            url: "{{ path('get_gestionnaire') }}",
            method: 'GET',
            dataType: 'json',
            success: function(data) {
                const select = $('.gestionnaire-select');
                select.each(function() {
                    var selected_id = $(this).attr('data-superviseur-id');
                    data.forEach(user => {
                        var is_selected = user.id == selected_id ? 'selected' : '';
                        const option = $('<option '+ is_selected +'></option>').val(user.id).text(user.username);
                        $(this).append(option);
                    });
                });
            },
            error: function(error) {
                console.error('Erreur lors du chargement des gestionnaires :', error);
            }
        });
    }

    $(document).ready(function() {
        fetchGestionnaire();
    });


    $(document).ready(function() {
        var hash = window.location.hash;
        if (!hash) return;

        // Extraction des paramètres depuis le hash
        var params = new URLSearchParams(hash.substring(1)); // Supprime le "#"
        var documentId = params.get('document-id');

        if (!documentId) return;

        setTimeout(function() {
            var row = $('tr[document-id="' + documentId + '"]');
            console.log('row:', row);
            if (row.length) {
                row[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                row.addClass('row-highlight');
                setTimeout(function() {
                    row.removeClass('row-highlight');
                    setTimeout(function() {
                        row.addClass('row-highlight');
                        setTimeout(function() {
                            row.removeClass('row-highlight');
                        }, 1000);
                    }, 1000);
                }, 1000);
            }
        }, 300);
    });


</script>

{# On définit une variable JS pour savoir si l'utilisateur est manager #}
<script>
    var isManager = {{ app.user.isManager|json_encode|raw }};
</script>

<style>
    /* Styles génériques pour le menu contextuel personnalisé */
    #custom-context-menu {
        position: absolute;
        display: none;
        background-color: #fff;
        border: 1px solid #ccc;
        box-shadow: 0 2px 5px rgba(0,0,0,0.5);
        z-index: 1000;
        width: 150px;
    }
    #custom-context-menu ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    #custom-context-menu li {
        padding: 8px 12px;
        cursor: pointer;
    }
    #custom-context-menu li:hover {
        background-color: #f0f0f0;
    }
</style>

<div id="custom-context-menu">
    <ul>
        <li id="delete-document">Supprimer le document</li>
    </ul>
</div>

<script>
if (isManager) {
    // Intercepter le clic droit sur les lignes du tableau
    $(document).on('contextmenu', '#table tbody tr', function(e) {
        e.preventDefault(); // Empêcher l'affichage du menu par défaut
        // Récupérer l'id du document à partir de l'attribut (ici "document-id")
        var documentId = $(this).attr('document-id');
        // Stocker l'ID dans le menu pour l'utiliser lors de la suppression
        $('#custom-context-menu').data('documentId', documentId);
        // Positionner le menu à l'endroit du clic et l'afficher
        $('#custom-context-menu').css({
            top: e.pageY + 'px',
            left: e.pageX + 'px',
            display: 'block'
        });
    });

    // Masquer le menu lorsqu'un clic se produit ailleurs sur la page
    $(document).click(function() {
        $('#custom-context-menu').hide();
    });

    // Action lors du clic sur l'option "Supprimer le document"
    $('#delete-document').on('click', function() {
        Swal.fire({
            title: 'Confirmer suppression',
            text: "Êtes-vous sûr de vouloir supprimer ce document ?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        }).then((result) => {
            if (result.isConfirmed) {
                var documentId = $('#custom-context-menu').data('documentId');
                $.ajax({
                    url: "{{ path('delete_document_ajax') }}",
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ id: documentId }),
                    success: function(response) {
                        if (response.status === 'success') {
                            // Retirer la ligne correspondante du tableau
                            $('#table tbody tr[document-id="' + documentId + '"]').remove();
                            Toast.fire({
                                icon: 'success',
                                title: 'Document supprimé avec succès'
                            });
                        } else {
                            Toast.fire({
                                icon: 'error',
                                title: 'Erreur : ' + response.message
                            });
                        }
                    },
                    error: function() {
                        Toast.fire({
                            icon: 'error',
                            title: 'Erreur lors de la suppression du document'
                        });
                    }
                });
            }
        });
    });
}

$(function () {
  const isAletiqEnabled = localStorage.getItem('aletiq') === 'true';
  if (!isAletiqEnabled) return;

  // Cache global : url → iframe unique
  const iframeCache = {};

  $('.preview-tooltip').each(function () {
    const $el = $(this);
    const url = $el.attr('href');

    // 1. Crée ou récupère l’iframe dans le cache
    if (!iframeCache[url]) {
      const $iframe = $(
        `<iframe src="${url}" style="border:none; width:600px; height:500px; display:none;"></iframe>`
      );
      $('body').append($iframe);
      iframeCache[url] = $iframe;
    }

    // 2. Initialise le popover en mode manuel (on fournira le contenu plus tard)
    $el.popover({
      html: true,
      trigger: 'manual',
      container: 'body',
      placement: 'top',
      sanitize: false,        // pour autoriser l’iframe telle quelle
      content: function () {
        // on retourne l’iframe DOM, pas une string
        return iframeCache[url];
      }
    })
    // 3. Survol → show + gestion de la sortie du popover
    .on('mouseenter', function () {
      const _this = this;
      $(_this).popover('show');
      // Quand la souris quitte le popover, on le cache
      $('.popover').on('mouseleave', function () {
        $(_this).popover('hide');
      });
      // Affiche l’iframe après insertion
      iframeCache[url].css('display', 'block');
    })
    // 4. Sortie du lien → délai puis hide si on n’est pas sur le popover
    .on('mouseleave', function () {
      const _this = this;
      setTimeout(function () {
        if (!$('.popover:hover').length) {
          // masque l’iframe et le popover
          iframeCache[url].css('display', 'none');
          $(_this).popover('hide');
        }
      }, 300);
    });
  });
});
</script>
