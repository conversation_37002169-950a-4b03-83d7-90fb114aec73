<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use App\Repository\UserRepository;
use App\Entity\User;
use App\Entity\Code;
use Symfony\Component\HttpFoundation\JsonResponse;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;

class UserController extends AbstractController
{
    #[Route('/user', name: 'app_user')]
    public function index(UserRepository $userRepository, EntityManagerInterface $em): Response
    {
        $users = $userRepository->findAll();

        $codes = $em->getRepository(Code::class)->createQueryBuilder('c')
            ->select('DISTINCT c.title, c.workCenter')
            ->orderBy('c.workCenter', 'ASC')
            ->getQuery()
            ->getResult();
        $workcenters = $codes;
        $workcenters = array_reduce($workcenters, function($acc, $code) {
            $acc[$code['workCenter']] = $code['title'];
            return $acc;
        }, []);

        return $this->render('user/index.html.twig', [
            'users' => $users,
            'workCenters' => $workcenters,
        ]);
    }

    #[Route('/gestionnaire', name: 'get_gestionnaire', methods: ['GET'])]
    public function gestionnaire(UserRepository $userRepository): JsonResponse
    {
        $users = $userRepository->findAll();
        $filteredUsers = array_filter($users, function($user) {
            return in_array('ROLE_Gestionnaire_Appro_Planning', $user->getRoles());
        });
        $users = $filteredUsers;
        $response = [];
        foreach ($users as $user) {
            $response[] = [
                'id' => $user->getId(),
                'username' => $user->getUsername(),
                'email' => $user->getEmail(),
                'roles' => $user->getRoles(),
            ];
        }
        return new JsonResponse($response);
    }

    #[Route('/user/imputation', name: 'get_user_imputation', methods: ['GET'])]
    public function getUserImputation(UserRepository $userRepository): JsonResponse
    {
        $users = $userRepository->findBy(['imputation' => true], ['nom' => 'ASC']);
        $response = [];
        foreach ($users as $user) {
            $response[] = $user->toArray();
        }
        return new JsonResponse($response);
    }

    #[Route('/user/no-imputation', name: 'get_user_no_imputation', methods: ['GET'])]
    public function getUserNoImputation(UserRepository $userRepository): JsonResponse
    {
        $users = $userRepository->createQueryBuilder('u')
            ->where('u.imputation = :false OR u.imputation IS NULL')
            ->setParameter('false', false)
            ->orderBy('u.nom', 'ASC')
            ->getQuery()
            ->getResult();
        $response = [];
        foreach ($users as $user) {
            $response[] = $user->toArray();
        }
        return new JsonResponse($response);
    }

    #[Route('/user/{id}/imputation', name: 'switch_user_imputation', methods: ['PUT'])]
    public function switchUserImputation(UserRepository $userRepository, $id, EntityManagerInterface $em): JsonResponse
    {
        $user = $userRepository->find($id);
        if (!$user) {
            return new JsonResponse(['error' => 'User not found'], Response::HTTP_NOT_FOUND);
        }
        $user->setImputation(!$user->isImputation());
        $em->persist($user);
        $em->flush();
        
        return new JsonResponse($user->toArray());
    }

    #[Route('/user/{id}/type/{type}', name: 'switch_user_check', methods: ['PUT'])]
    public function switchUserCheck(UserRepository $userRepository, $id, $type, EntityManagerInterface $em): JsonResponse
    {
        $user = $userRepository->find($id);
        if (!$user) {
            return new JsonResponse(['error' => 'User not found'], Response::HTTP_NOT_FOUND);
        }
        if ($type === 'ci') {
            $user->setCi(!$user->isCi());
        } else if ($type === 'sap') {
            $user->setSap(!$user->isSap());
        } else {
            return new JsonResponse(['error' => 'Type not found'], Response::HTTP_NOT_FOUND);
        }
        $em->persist($user);
        $em->flush();
        
        return new JsonResponse($user->toArray());
    }

    #[Route('/user/{id}/workcenter', name: 'switch_user_workcenter', methods: ['PUT'])]
    public function switchUserWorkCenter(UserRepository $userRepository, $id, EntityManagerInterface $em, Request $request): JsonResponse
    {
        $user = $userRepository->find($id);
        if (!$user) {
            return new JsonResponse(['error' => 'User not found'], Response::HTTP_NOT_FOUND);
        }
        $workCenter = $request->get('workCenter');
        $user->setWorkCenter($workCenter);
        $em->persist($user);
        $em->flush();
        
        return new JsonResponse($user->toArray());
    }

    // get qual_user
    #[Route('/user/qual_user', name: 'get_qual_user', methods: ['GET'])]
    public function getQualUser(UserRepository $userRepository): JsonResponse
    {
        $qualUsername = ['mvrignaud', 'rbonin', 'fbellon'];
        $users = $userRepository->findBy(['username' => $qualUsername]);
        $response = [];
        foreach ($users as $user) {
            $response[] = [
                'id' => $user->getId(),
                'username' => $user->getUsername(),
                'nom' => $user->getNom(),
                'prenom' => $user->getPrenom(),
            ];
        }
        return new JsonResponse($response);
    }

}
