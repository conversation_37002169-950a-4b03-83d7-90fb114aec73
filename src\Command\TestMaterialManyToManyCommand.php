<?php

namespace App\Command;

use App\Entity\Document;
use App\Entity\Material;
use App\Repository\DocumentRepository;
use App\Repository\MaterialRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:test-material-many-to-many',
    description: 'Teste les relations Many-to-Many Document ↔ Material'
)]
class TestMaterialManyToManyCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $em,
        private DocumentRepository $documentRepository,
        private MaterialRepository $materialRepository
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Test des relations Many-to-Many Document ↔ Material...</info>');

        // 1. Vérifier le nombre de relations dans la table document_materials
        $relationsCount = $this->em->getConnection()->fetchOne('SELECT COUNT(*) FROM document_materials');
        $output->writeln('<info>Relations dans document_materials: ' . $relationsCount . '</info>');

        // 2. Tester la récupération d'un document avec ses matériaux
        $documents = $this->documentRepository->findBy([], [], 3);
        $output->writeln('<info>Documents trouvés: ' . count($documents) . '</info>');

        foreach ($documents as $document) {
            $materials = $document->getMaterials();
            $output->writeln('<comment>Document ' . $document->getReference() . ':</comment>');
            $output->writeln('<comment>  - Nombre de matériaux: ' . $materials->count() . '</comment>');
            
            foreach ($materials as $material) {
                $output->writeln('<comment>  - Matériau: ' . $material->getReference() . ' (' . $material->getDescription() . ')</comment>');
            }

            // Test de la méthode de compatibilité getMaterial()
            $materialRef = $document->getMaterial();
            $output->writeln('<comment>  - getMaterial() (compatibilité): ' . ($materialRef ?? 'null') . '</comment>');

            // Test de la méthode getMaterialsArray()
            $materialsArray = $document->getMaterialsArray();
            $output->writeln('<comment>  - getMaterialsArray(): [' . implode(', ', $materialsArray) . ']</comment>');
        }

        // 3. Tester la relation inverse (Material → Documents)
        $materials = $this->materialRepository->findBy([], [], 3);
        $output->writeln('<info>Test de la relation inverse Material → Documents:</info>');

        foreach ($materials as $material) {
            $documents = $material->getDocuments();
            $output->writeln('<comment>Matériau ' . $material->getReference() . ':</comment>');
            $output->writeln('<comment>  - Nombre de documents: ' . $documents->count() . '</comment>');
            
            foreach ($documents as $document) {
                $output->writeln('<comment>  - Document: ' . $document->getReference() . '</comment>');
            }
        }

        // 4. Test de sérialisation JSON
        if (!empty($documents)) {
            $firstDocument = $documents[0];
            $json = $firstDocument->toJson();
            $output->writeln('<info>Test de sérialisation JSON:</info>');
            
            if (isset($json['material'])) {
                $output->writeln('<comment>  - material (premier): ' . json_encode($json['material']) . '</comment>');
            }
            
            if (isset($json['materials'])) {
                $output->writeln('<comment>  - materials (tous): ' . count($json['materials']) . ' matériaux</comment>');
            }
        }

        // 5. Vérifier l'intégrité des données
        $documentsWithMaterials = $this->em->getConnection()->fetchOne('
            SELECT COUNT(DISTINCT d.id) 
            FROM document d 
            INNER JOIN document_materials dm ON d.id = dm.document_id
        ');
        $output->writeln('<info>Documents avec matériaux: ' . $documentsWithMaterials . '</info>');

        $materialsWithDocuments = $this->em->getConnection()->fetchOne('
            SELECT COUNT(DISTINCT m.id) 
            FROM material m 
            INNER JOIN document_materials dm ON m.id = dm.material_id
        ');
        $output->writeln('<info>Matériaux avec documents: ' . $materialsWithDocuments . '</info>');

        $output->writeln('<info>Test terminé avec succès !</info>');
        return Command::SUCCESS;
    }
}
