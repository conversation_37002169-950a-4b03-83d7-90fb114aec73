/**
 * Gestionnaire de datalist pour les matériaux
 */
class MaterialDatalist {
    constructor(inputElement, options = {}) {
        this.input = inputElement;
        this.options = {
            minLength: 0, // Changé à 0 pour afficher dès le clic
            maxResults: 50,
            searchUrl: '/index.php/api/materials/search', // URL absolue
            ...options
        };

        this.datalist = null;
        this.selectedMaterials = new Set();
        this.init();
    }

    init() {
        this.createDatalist();
        this.bindEvents();

        // Si l'input a déjà une valeur, charger les matériaux correspondants
        if (this.input.value) {
            this.loadMaterials(this.input.value);
        }
    }

    createDatalist() {
        // <PERSON><PERSON>er ou récupérer la datalist
        let datalistId = this.input.getAttribute('list');
        if (!datalistId) {
            datalistId = 'materials-datalist-' + Math.random().toString(36).substring(2, 11);
            this.input.setAttribute('list', datalistId);
        }

        this.datalist = document.getElementById(datalistId);
        if (!this.datalist) {
            this.datalist = document.createElement('datalist');
            this.datalist.id = datalistId;
            document.body.appendChild(this.datalist);
        }
    }

    bindEvents() {
        let searchTimeout;

        // Charger tous les matériaux au focus
        this.input.addEventListener('focus', () => {
            if (this.datalist.children.length === 0) {
                this.loadMaterials(''); // Charger tous les matériaux
            }
        });

        this.input.addEventListener('input', (e) => {
            const query = e.target.value.trim();

            clearTimeout(searchTimeout);

            searchTimeout = setTimeout(() => {
                this.loadMaterials(query);
            }, 300); // Debounce de 300ms
        });

        // Gérer la sélection d'un matériau
        this.input.addEventListener('change', (e) => {
            const selectedValue = e.target.value;
            if (selectedValue) {
                this.selectMaterial(selectedValue);
            }
        });

        // Maintenir la datalist visible après sélection
        this.input.addEventListener('blur', () => {
            // Petit délai pour permettre la sélection
            setTimeout(() => {
                if (this.input.value === '') {
                    this.loadMaterials('');
                }
            }, 200);
        });
    }

    async loadMaterials(query) {
        try {
            const url = `${this.options.searchUrl}?q=${encodeURIComponent(query)}&limit=${this.options.maxResults}`;
            const response = await fetch(url);

            if (!response.ok) {
                throw new Error(`Erreur API ${response.status}`);
            }

            const materials = await response.json();
            this.updateDatalist(materials);
        } catch (error) {
            console.error('Erreur lors du chargement des matériaux:', error);
        }
    }

    updateDatalist(materials) {
        // Vider la datalist
        this.datalist.innerHTML = '';

        // Ajouter les nouvelles options
        materials.forEach(material => {
            const option = document.createElement('option');
            option.value = material.reference;
            option.textContent = material.label;
            option.dataset.materialId = material.id;
            this.datalist.appendChild(option);
        });
    }

    clearDatalist() {
        if (this.datalist) {
            this.datalist.innerHTML = '';
        }
    }

    selectMaterial(reference) {
        // Trouver l'option correspondante dans la datalist
        const option = this.datalist.querySelector(`option[value="${reference}"]`);
        if (option) {
            const materialId = option.dataset.materialId;

            // Émettre un événement personnalisé
            const event = new CustomEvent('materialSelected', {
                detail: {
                    id: materialId,
                    reference: reference,
                    label: option.textContent
                }
            });
            this.input.dispatchEvent(event);
        }
    }

    // Méthode pour ajouter un matériau à la liste des sélectionnés
    addSelectedMaterial(materialData) {
        this.selectedMaterials.add(materialData);

        // Émettre un événement pour notifier l'ajout
        const event = new CustomEvent('materialAdded', {
            detail: materialData
        });
        this.input.dispatchEvent(event);
    }

    // Méthode pour supprimer un matériau de la liste des sélectionnés
    removeSelectedMaterial(materialId) {
        this.selectedMaterials.forEach(material => {
            if (material.id == materialId) {
                this.selectedMaterials.delete(material);
            }
        });

        // Émettre un événement pour notifier la suppression
        const event = new CustomEvent('materialRemoved', {
            detail: { id: materialId }
        });
        this.input.dispatchEvent(event);
    }

    // Obtenir la liste des matériaux sélectionnés
    getSelectedMaterials() {
        return Array.from(this.selectedMaterials);
    }
}

// Fonction d'initialisation globale
window.initMaterialDatalist = function(selector, options = {}) {
    const inputs = document.querySelectorAll(selector);
    const instances = [];

    inputs.forEach(input => {
        instances.push(new MaterialDatalist(input, options));
    });

    return instances;
};

// Auto-initialisation pour les éléments avec la classe 'material-datalist'
document.addEventListener('DOMContentLoaded', function() {
    window.initMaterialDatalist('.material-datalist');
});
