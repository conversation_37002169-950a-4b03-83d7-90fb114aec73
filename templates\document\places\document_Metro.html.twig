{% extends 'base.html.twig' %}

{% block title %}Documents{% endblock %}

{% block body %}

<style>
#table-container {
    padding: 1rem;
    background-color: #F8F9FA;
    border-radius: 0.5rem;
}

#table tr:last-child {
    border: none!important;
}

.table-filter-input {
    width: 100%;
    font-size: 0.85rem;
    height: calc(1.8rem + 2px);
}

#table td span {
    cursor: pointer;
    transition: all 0.3s;
}

#table td span:hover {
    padding: 0.375rem 0.75rem;
}

#table th, #table td {
    vertical-align: middle!important;
    white-space: nowrap;
    text-align: center!important;
    padding: 0.15rem!important;
    border: none!important;
}

#table thead th {
    user-select: none;
}

#table thead tr {
    border: none;
}

#table thead tr#entetes th {
    background-color: #004080;
    color: #fff;
    font-size: 0.85rem;
}

#table thead tr#filtres th {
    background-color: #F8F9FA; /* gris clair */
    border: none;
    cursor: pointer;
}

/* Icônes de tri */
th.sort-asc i, th.sort-desc i {
    margin-left: 5px;
}

/* Badges */
.badge.bg-primary {
    background: #0059B3!important;
}

/* Champs invalides */
.is-invalid {
    border-color: #dc3545;
}

/* Boutons */
.btn-refresh {
    margin-bottom: 1rem;
}

/* --- Modale personnalisée --- */
.modal-dialog.modal-lg {
    max-width: 900px;
}

.modal-content.custom-modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.modal-header.custom-modal-header {
    background-color: #004080;
    color: #fff;
    border-bottom: none;
}

.modal-header.custom-modal-header .btn-close {
    filter: invert(100%); /* Rendre la croix blanche sur fond bleu */
}

.modal-footer.custom-modal-footer {
    border-top: none;
}

.tooltip .tooltip-inner {
    max-width: 400px;
    overflow-y: auto; /* Barre de défilement si dépasse */
    white-space: normal; /* Permet de passer à la ligne */
}

/* Styles pour le modal des commentaires */
.comment-item {
    background-color: #f8f9fa;
    border-left: 4px solid #009BFF;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.comment-item:last-child {
    margin-bottom: 0;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.comment-state {
    background-color: #009BFF;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.comment-meta {
    color: #6c757d;
    font-size: 0.9rem;
}

.comment-content {
    color: #333;
    line-height: 1.5;
    margin-top: 8px;
    padding: 10px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.no-comments {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 40px 20px;
}

.comments-count {
    background-color: #e9ecef;
    color: #495057;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    margin-bottom: 20px;
    display: inline-block;
}

/* Styles pour le formulaire d'ajout de commentaire */
.modal-footer.custom-modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 15px 20px;
}

#newCommentText {
    resize: vertical;
    min-height: 38px;
}

#addCommentBtn {
    background-color: #009BFF;
    border-color: #009BFF;
    height: 38px;
}

#addCommentBtn:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.comment-form-loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Retirer la transparence des tooltips pour une meilleure lisibilité */
.tooltip .tooltip-inner {
    background-color: rgba(0, 0, 0, 0.95) !important;
    opacity: 1 !important;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before,
.tooltip.bs-tooltip-bottom .tooltip-arrow::before,
.tooltip.bs-tooltip-start .tooltip-arrow::before,
.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-color: rgba(0, 0, 0, 0.95) transparent !important;
}

</style>

<div class="mt-3" style="margin: 0 2%">
    <div class="row">
        <div class="col">
            <h3 class="mb-2">Métrologies</h3>

            {% if documents is empty %}
                <div class="alert alert-warning" role="alert">
                    Aucun document trouvé.
                </div>
            {% else %}
                <div class="card shadow border-0">
                    <div class="card-body p-0">
                        <div id="table-container">
                            <table class="table table-hover table-bordered mb-0" id="table">
                                <thead>
                                    <!-- Ligne de filtres -->
                                    <tr id="filtres">
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="#" data-col="0"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Pack" data-col="1"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Activité" data-col="2"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Référence" data-col="3"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="R" data-col="4"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="prod plan" data-col="5"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="R" data-col="6"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Action" data-col="7"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Inventaire" data-col="8"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Type" data-col="9"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="prod Sup" data-col="10"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Mat Type" data-col="11"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="MOF" data-col="12"></th>
                                        <th ><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Commentaires" data-col="13"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Control Type" data-col="14"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="time" data-col="15"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Étapes" data-col="16"></th>
                                        <th><span id="signer-mass" style="display: none" class="badge bg-success" onclick="signSelectedDocuments()">Signer sélectionnés</span></th>
                                        <th><span class="badge bg-secondary">{{ documents|length }}{{ documents|length == 1 ? ' Document' : ' Documents' }}</span></th>
                                    </tr>
                                    <!-- Ligne d'entêtes -->
                                    <tr id="entetes">
                                        <th>#</th>
                                        <th>Pack</th>
                                        <th>Activité</th>
                                        <th>Référence</th>
                                        <th>R</th>
                                        <th>prod plan</th>
                                        <th>R</th>
                                        <th>Action</th>
                                        <th>Inventaire</th>
                                        <th>Type</th>
                                        <th>prod Sup</th>
                                        <th>Mat Type</th>
                                        <th>MOF</th>
                                        <th >Commentaires</th>
                                        <th>Control Type</th>
                                        <th>time en min</th>
                                        <th>Révisions</th>
                                        <th>Validations</th>
                                        <th>Visas</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for document in documents %}
                                        <tr document-id="{{document.id}}">
                                            <td>
                                            {% if document.stateTimestamps is not null %}
                                                {% if document.stateTimestamps[place] is defined %}
                                                    {% set arrivedDate = date(document.stateTimestamps[place]) %}
                                                    {% set diff = date().diff(arrivedDate) %}
                                                    <span class="badge bg-primary">{{ diff.days }} jour{{ diff.days > 1 ? 's' }}</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">N/A</span>
                                                {% endif %}

                                            {% else %}
                                                <span class="badge bg-secondary">N/A</span>
                                            {% endif %}
                                                {# {{document.getDaysInState(place)}} #}
                                            </td>
                                            <td><a href="{{ path('detail_package', {'id': document.relPack.id}) }}" class="badge bg-primary pack-link">{{document.relPack.id}}</a></td>
                                            <td>
                                                <table class="table table-striped table-bordered mb-0 text-center" style="font-size: 0.7rem;">
                                                    <tbody>
                                                        <tr>
                                                            <td class="p-1" style="font-size: 0.70rem;"> {{ document.relPack.activity }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="p-1" style="font-size: 0.70rem;"> {{ document.relPack.getProjectRelation() ? document.relPack.getProjectRelation.otp() : '' }}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                            <td>{{ document.reference|trim }}</td>
                                            <td>
                                                {{ document.refrev }}
                                                {% if document.ex != 'NO' %}
                                                    <span style="color: red; font-weight: 500;"><sup>{{ document.ex }}</sup></span>
                                                {% else %}
                                                    <sup>{{ document.ex }}</sup>
                                                {% endif %}
                                            </td>
                                            <td class="p-1" style="font-size: 0.80rem;">
                                                <a href="https://app.aletiq.com/parts/preview/id/{{ document.prodDraw }}/revision/{{ document.prodDrawRev }}"
                                                    target="_blank"
                                                    class="badge bg-primary preview-tooltip">
                                                    {{ document.prodDraw }}
                                                </a>
                                            </td>

                                            {# <td class="p-1" style="font-size: 0.80rem;"><a href="https://app.aletiq.com/parts/preview/id/{{ document.prodDraw }}/revision/{{ document.prodDrawRev }}" target="_blank" class="badge bg-primary">{{ document.prodDraw }}</a></td> #}
                                            <td>{{ document.prodDrawRev }}</td>
                                            <td>{{ document.action }}</td>
                                            <td>{{ document.getInventoryImpact }}</td>
                                            <td>
                                                <table class="table table-striped table-bordered mb-0 text-center" style="font-size: 0.8rem;">
                                                    <tbody>
                                                        <tr>
                                                            <td class="p-1" style="font-size: 0.75rem;"><strong>doc type: </strong>{{ document.doctype }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="p-1" style="font-size: 0.75rem;">
                                                                <strong>internal mach : </strong>
                                                                {% if document.internalMachRec %}
                                                                    <span class="badge bg-success">Oui</span>
                                                                {% else %}
                                                                    <span class="badge bg-danger">Non</span>
                                                                {% endif %}
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                            <td>{{ document.prodAgent }}</td>
                                            <td>{{ document.matProdType }}</td>
                                            <td>{{ document.mof }}</td>

                                            <td>
                                            {% if document.commentaires|length > 0 %}
                                                <i class="fas fa-file-alt"
                                                style="color: #009BFF; cursor: pointer;"
                                                data-bs-toggle="tooltip"
                                                data-document-id="{{ document.id }}"
                                                onmouseenter="loadCommentsTooltip(this)"
                                                onclick="showCommentsModal({{ document.id }}, '{{ document.reference|escape('js') }}')"
                                                title="Cliquer pour voir les commentaires ({{ document.commentaires|length }}) | Survoler pour aperçu"
                                                txt="{% for comment in document.commentaires %}
                                                <strong>{{ comment.state|upper|e }}</strong> : {{ comment.commentaire|e }}
                                                par <em>{{ comment.user|e }}</em><br>
                                                {% endfor %}"
                                                ></i>
                                            {% else %}
                                                <i class="fas fa-file-alt"
                                                style="color: #ccc; cursor: pointer;"
                                                title="Aucun commentaire"
                                                data-bs-toggle="tooltip"
                                                data-document-id="{{ document.id }}"
                                                onmouseenter="loadCommentsTooltip(this)"
                                                onclick="showCommentsModal({{ document.id }}, '{{ document.reference|escape('js') }}')"
                                                title="Cliquer pour voir les commentaires ({{ document.commentaires|length }}) | Survoler pour aperçu"
                                                txt="{% for comment in document.commentaires %}
                                                <strong>{{ comment.state|upper|e }}</strong> : {{ comment.commentaire|e }}
                                                par <em>{{ comment.user|e }}</em><br>
                                                {% endfor %}"
                                                ></i>
                                            {% endif %}
                                            </td>
                                            <td>
                                                <div class="form-check">
                                                    <input class="form-check-input metro-control-checkbox" data-id={{document.id}} type="checkbox" value="P1" name='metroControl[]' id="controlP1{{ document.id }}" {% if 'P1' in document.metroControl %}checked{% endif %}>
                                                    <label class="form-check-label badge bg-primary" for="controlP1{{ document.id }}">P1</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input metro-control-checkbox" data-id={{document.id}} type="checkbox" value="3D final" name='metroControl[]' id="control3Dfinal{{ document.id }}" {% if '3D final' in document.metroControl %}checked{% endif %}>
                                                    <label class="form-check-label badge bg-primary" for="control3Dfinal{{ document.id }}">3D final</label>
                                                </div>
                                            </td>
                                            <td>
                                                <input type="number"
                                                class="form-control form-control-sm metrotime-input"
                                                doc-id="{{ document.id }}"
                                                value="{{ document.metrotime }}"
                                                />
                                                </td>

                                            <td class="text-center">
                                                {% if document.CurrentStepsVisa|length > 1 %}
                                                    <div class="dropdown">
                                                        <span id="dropdownMenuButton{{ document.id }}" data-bs-toggle="dropdown" aria-expanded="false" class="badge bg-primary">{{ document.CurrentStepsVisa|length }}</span>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ document.id }}">
                                                            {% for step, value in document.CurrentStepsVisa %}
                                                                <li><a class="dropdown-item" href="{{ path('app_document_place', {'place': step}) }}">{{ step|replace({'_': ' '})|first|upper ~ step|replace({'_': ' '})|slice(1) }}</a></li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                {% else %}
                                                    {% for step, value in document.CurrentStepsVisa %}
                                                        <span class="badge bg-primary">{{ step }}</span>
                                                    {% endfor %}
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="d-flex justify-content-center align-items-center">
                                                    <div class="form-check">
                                                        <input class="form-check-input doc-select" type="checkbox" data-document-id="{{ document.id }}" data-current-steps='{{ document.CurrentStepsVisa|json_encode|e('html_attr') }}'>
                                                    </div>
                                                    <span class="badge bg-primary" onclick='createVisa("{{ document.id|escape('js') }}")'>Signe</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary" onclick="showVisas({{ document.id }})"><i class="fa-solid fa-passport"></i></span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div><!-- ./table-responsive -->
                    </div><!-- ./card-body -->
                </div><!-- ./card -->
                {# Pagination supprimée #}
            {% endif %}
        </div><!-- ./col -->
    </div><!-- ./row -->
</div><!-- ./container -->

<div class="modal fade" id="modalVisas" tabindex="-1" aria-labelledby="modalVisasLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-lg">
        <div class="modal-content custom-modal-content">
            <div class="modal-header custom-modal-header">
                <h5 class="modal-title" id="modalVisasLabel">Historique des visas</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>

<!-- Modal pour les commentaires -->
<div class="modal fade" id="modalComments" tabindex="-1" aria-labelledby="modalCommentsLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-lg">
        <div class="modal-content custom-modal-content">
            <div class="modal-header custom-modal-header">
                <h5 class="modal-title" id="modalCommentsLabel">
                    <i class="fas fa-comments me-2"></i>
                    Commentaires - <span id="documentReference"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="commentsModalBody">
                <!-- Les commentaires seront chargés ici -->
            </div>
            <div class="modal-footer custom-modal-footer">
                <div class="w-100">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <span class="badge bg-primary" style="background-color: #009BFF !important; font-size: 0.9rem; padding: 8px 12px;">
                                {{ place|replace({'_': ' '})|title }}
                            </span>
                        </div>
                        <div class="col-md-8">
                            <textarea class="form-control form-control-sm"
                                      id="newCommentText"
                                      placeholder="Ajouter un commentaire pour {{ place|replace({'_': ' '})|title }}..."
                                      rows="2"></textarea>
                        </div>
                        <div class="col-md-2">
                            <button type="button"
                                    class="btn btn-primary btn-sm w-100"
                                    id="addCommentBtn"
                                    onclick="addNewComment()">
                                <i class="fas fa-plus me-1"></i>
                                Ajouter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Fonction pour afficher le modal des commentaires
    function showCommentsModal(documentId, documentReference) {
        // Stocker l'ID du document pour l'ajout de commentaires
        currentDocumentId = documentId;

        // Mettre à jour le titre du modal
        document.getElementById('documentReference').textContent = documentReference;

        // Afficher le modal
        const modal = new bootstrap.Modal(document.getElementById('modalComments'));
        modal.show();

        // Charger les commentaires
        loadCommentsForModal(documentId);
    }



    // Variable globale pour stocker l'ID du document actuel
    let currentDocumentId = null;

    // Fonction pour ajouter un nouveau commentaire (réutilise la logique de jhess)
    function addNewComment() {
        const commentTextarea = document.getElementById('newCommentText');
        const commentText = commentTextarea.value.trim();

        if (!commentText) {
            Toast.fire({
                icon: 'warning',
                title: 'Veuillez saisir un commentaire'
            });
            return;
        }

        if (!currentDocumentId) {
            Toast.fire({
                icon: 'error',
                title: 'Erreur: Document non identifié'
            });
            return;
        }

        // Désactiver le formulaire pendant l'envoi
        const form = document.querySelector('.modal-footer');
        form.classList.add('comment-form-loading');
        document.getElementById('addCommentBtn').innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Ajout...';

        // Utiliser la fonction addComment existante de jhess
        // Créer un élément temporaire pour simuler l'input du tableau
        const tempInput = $('<input>').val(commentText).data('document-id', currentDocumentId);

        // Sauvegarder les fonctions originales de jhess
        const originalShowLoading = window.showLoading;
        const originalHideLoading = window.hideLoading;
        const originalRefreshTable = window.refreshTable;

        // Remplacer temporairement les fonctions pour le modal
        window.showLoading = function() { /* déjà géré par le formulaire */ };
        window.hideLoading = function() {
            form.classList.remove('comment-form-loading');
            document.getElementById('addCommentBtn').innerHTML = '<i class="fas fa-plus me-1"></i>Ajouter';

            // Vider le champ de commentaire
            commentTextarea.value = '';

            // Recharger les commentaires dans le modal après succès
            setTimeout(() => {
                loadCommentsForModal(currentDocumentId);
            }, 200);
        };
        window.refreshTable = function() {
            // Ne rien faire pour éviter de rafraîchir le tableau
        };

        // Appeler la fonction addComment de jhess
        addComment(currentDocumentId, tempInput[0]);

        // Restaurer les fonctions originales après un délai
        setTimeout(() => {
            window.showLoading = originalShowLoading;
            window.hideLoading = originalHideLoading;
            window.refreshTable = originalRefreshTable;
        }, 1000);
    }

    // Fonction séparée pour charger les commentaires (réutilisable)
    function loadCommentsForModal(documentId) {
        document.getElementById('commentsModalBody').innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <p class="mt-2 text-muted">Chargement des commentaires...</p>
            </div>
        `;

        $.ajax({
            url: "{{ path('app_commentaire_get', {'documentId': 'DOCUMENT_ID_PLACEHOLDER'}) }}".replace('DOCUMENT_ID_PLACEHOLDER', documentId),
            type: 'GET',
            success: function(comments) {
                displayComments(comments);
            },
            error: function(xhr, status, error) {
                document.getElementById('commentsModalBody').innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Erreur lors du chargement des commentaires: ${error}
                    </div>
                `;
            }
        });
    }

    // Rendre les fonctions globales pour qu'elles soient accessibles depuis le HTML
    window.showCommentsModal = showCommentsModal;
    window.addNewComment = addNewComment;
</script>


{% include 'js/jhess.html.twig' %}
{% endblock %}

