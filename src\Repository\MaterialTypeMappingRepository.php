<?php

namespace App\Repository;

use App\Entity\MaterialTypeMapping;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<MaterialTypeMapping>
 */
class MaterialTypeMappingRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MaterialTypeMapping::class);
    }

    /**
     * Trouve tous les mappings actifs
     */
    public function findActive(): array
    {
        return $this->createQueryBuilder('m')
            ->andWhere('m.isActive = :active')
            ->setParameter('active', true)
            ->orderBy('m.displayLabel', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve le code SAP par libellé d'affichage
     */
    public function findSapCodeByDisplayLabel(string $displayLabel): ?string
    {
        $result = $this->createQueryBuilder('m')
            ->select('m.sapCode')
            ->andWhere('m.displayLabel = :label')
            ->andWhere('m.isActive = :active')
            ->setParameter('label', $displayLabel)
            ->setParameter('active', true)
            ->getQuery()
            ->getOneOrNullResult();

        return $result ? $result['sapCode'] : null;
    }

    /**
     * Trouve le libellé d'affichage par code SAP
     */
    public function findDisplayLabelBySapCode(string $sapCode): ?string
    {
        $result = $this->createQueryBuilder('m')
            ->select('m.displayLabel')
            ->andWhere('m.sapCode = :code')
            ->andWhere('m.isActive = :active')
            ->setParameter('code', $sapCode)
            ->setParameter('active', true)
            ->getQuery()
            ->getOneOrNullResult();

        return $result ? $result['displayLabel'] : null;
    }

    /**
     * Retourne un tableau associatif [displayLabel => sapCode]
     */
    public function getMappingArray(): array
    {
        $results = $this->createQueryBuilder('m')
            ->select('m.displayLabel, m.sapCode')
            ->andWhere('m.isActive = :active')
            ->setParameter('active', true)
            ->orderBy('m.displayLabel', 'ASC')
            ->getQuery()
            ->getResult();

        $mapping = [];
        foreach ($results as $result) {
            $mapping[$result['displayLabel']] = $result['sapCode'];
        }

        return $mapping;
    }
}
