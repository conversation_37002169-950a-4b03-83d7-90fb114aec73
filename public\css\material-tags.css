/* Styles pour le composant MaterialTags */

.material-tags-wrapper {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.5rem;
    background-color: #fff;
    min-height: 2.5rem;
}

.material-tags-wrapper:focus-within {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.material-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
}

.material-tags-container:empty {
    margin-bottom: 0;
}

.material-tag {
    display: inline-flex;
    align-items: center;
    background-color: #e3f2fd !important;
    border: 1px solid #90caf9 !important;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    color: #1565c0 !important;
    font-weight: 500;
}

.material-tag .btn-close {
    background: none;
    border: none;
    color: #1565c0 !important;
    font-size: 0.75rem;
    line-height: 1;
    margin-left: 0.5rem;
    cursor: pointer;
    padding: 0;
    width: 0.75rem;
    height: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
}

.material-tag .btn-close:hover {
    color: #dc3545 !important;
    opacity: 1;
}

.material-tag-remove {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 1rem;
    line-height: 1;
    margin-left: 0.5rem;
    cursor: pointer;
    padding: 0;
    width: 1rem;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.material-tag-remove:hover {
    color: #dc3545;
}

/* Styles pour les champs obligatoires FMTS */
.fmts-required {
    border-left: 4px solid #ff6b35 !important;
    background-color: #fff8f5 !important;
}

.fmts-required::before {
    content: "FMTS requis";
    position: absolute;
    top: -20px;
    left: 0;
    font-size: 11px;
    color: #ff6b35;
    font-weight: 500;
    background: white;
    padding: 2px 6px;
    border-radius: 3px;
    z-index: 10;
}

.material-input-container input {
    border: none;
    outline: none;
    background: transparent;
    flex: 1;
    min-width: 200px;
}

.material-input-container input:focus {
    box-shadow: none;
}

/* Animation pour l'ajout/suppression de tags */
.material-tag {
    animation: fadeIn 0.2s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Style pour les tags en mode lecture seule */
.material-tag.readonly {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.material-tag.readonly .material-tag-remove {
    display: none;
}

/* Responsive */
@media (max-width: 576px) {
    .material-input-container input {
        min-width: 150px;
    }
}
