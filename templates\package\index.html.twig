{% extends 'base.html.twig' %}

{% block title %}Package{% endblock %}

{% block body %}
<style>
    td {
        text-align: center;
        vertical-align: middle;
        font-size: 14px;
    }
    th {
        text-align: center;
        vertical-align: middle;
        background-color: #004080!important;
        color: white!important;
        border: 1px solid #004080!important;
        border-top: none!important;
        font-size: 12px!important;
    }
    .badge-hover:hover {
        background-color: #004080!important;
        transition: background-color 0.3s ease;
    }
    .badge-hover {
        transition: background-color 0.3s ease;
        cursor: pointer;
    }
    #packageTabs .nav-link {
        color: #004080;
        background-color: #f8f9fa;
        border: none;
        border-top-left-radius: 0.25rem;
        border-top-right-radius: 0.25rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    #packageTabs .nav-link:hover {
        color: #fff;
        background-color: rgba(0, 64, 128, 0.8);
    }
    #packageTabs .nav-link.active {
        color: #fff;
        background-color: #004080;
        border: none;
        border-top-left-radius: 0.25rem;
        border-top-right-radius: 0.25rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
</style>

<div class="mt-2" style="margin-left: 10px; margin-right: 10px;">
    <div class="d-flex justify-content-between align-items-center">
        <h4 class="mb-0">Packages</h4>
        <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#create-package">Créer un package</button>
    </div>
    <hr class="my-2">

    <!-- Nav Tabs -->
    <ul class="nav nav-tabs border-0" id="packageTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="update-tab" data-bs-toggle="tab" data-bs-target="#update" type="button" role="tab">
                Update <span class="badge bg-primary">{{ BE_0|length }}</span>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="verification-tab" data-bs-toggle="tab" data-bs-target="#verification" type="button" role="tab">
                Vérification <span class="badge bg-primary">{{ BE_1|length }}</span>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="validation-tab" data-bs-toggle="tab" data-bs-target="#validation" type="button" role="tab">
                Validation <span class="badge bg-primary">{{ BE|length }}</span>
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="packageTabsContent">
        <!-- Package Update -->
        <div class="tab-pane fade show active" id="update" role="tabpanel">
            {% if BE_0|length == 0 %}
                <div class="alert alert-warning mt-2 mx-5" role="alert">
                    Aucun package à afficher
                </div>
            {% else %}
                <table class="table table-hover table-sm table-bordered">
                    <thead>
                        <tr>
                            <th>N°</th>
                            <th>Propriétaire</th>
                            <th>Projet</th>
                            <th>Activité</th>
                            <th>DMO</th>
                            <th>Ex</th>
                            <th>Date de réservation</th>
                            <th>#</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for package in BE_0 %}
                            <tr package-id="{{ package.id }}" link="{{ path('update_package', {'id': package.id}) }}">
                                <td>{{ package.id }}</td>
                                <td>{{ package.owner }}</td>
                                <td>{{ package.getProjectRelation is not null and package.getProjectRelation.getOTP is not null ? package.getProjectRelation.getOTP : '' }}</td>
                                <td>{{ package.activity }}</td>
                                <td>
                                    {# On boucle sur package.dmos, la vraie relation ManyToMany #}
                                    {% set dmoCount = package.dmos|length %}
                                    {% if dmoCount > 2 %}
                                        <div class="dropdown">
                                            <span class="badge bg-secondary dropdown-toggle"
                                                id="dropdownMenuButton{{ package.id }}"
                                                data-bs-toggle="dropdown"
                                                aria-expanded="false">
                                                {{ dmoCount }} DMO
                                            </span>
                                            <ul class="dropdown-menu text-center shadow"
                                                aria-labelledby="dropdownMenuButton{{ package.id }}">
                                                {% for dmo in package.dmos %}
                                                    <li>
                                                        <a href="{{ path('app_dmo_show', {'id': dmo.getId()}) }}"
                                                           target="_blank"
                                                           class="badge bg-primary badge-hover"
                                                           style="text-decoration: none;">
                                                            {{ dmo.getDmoId() }}
                                                        </a>
                                                    </li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                    {% else %}
                                        {% for dmo in package.dmos %}
                                            <a href="{{ path('app_dmo_show', {'id': dmo.getId()}) }}"
                                               target="_blank"
                                               class="badge bg-primary badge-hover"
                                               style="text-decoration: none;">
                                                {{ dmo.getDmoId() }}
                                            </a>
                                        {% endfor %}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if package.ex != 'NO' %}
                                        <span style="color: red; font-weight: 500;">{{ package.ex }}</span>
                                    {% else %}
                                        {{ package.ex }}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if package.ReservationDate is not null %}
                                        {{ package.ReservationDate|date('d/m/Y') }}
                                    {% endif %}
                                </td>
                                <td>{{ package.documents|length }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% endif %}
        </div>
        
        <!-- Package Verification -->
        <div class="tab-pane fade" id="verification" role="tabpanel">
            {% if BE_1|length == 0 %}
                <div class="alert alert-warning mt-2 mx-5" role="alert">
                    Aucun package à vérifier
                </div>
            {% else %}
                <table class="table table-hover table-sm table-bordered">
                    <thead>
                        <tr>
                            <th>N°</th>
                            <th>Propriétaire</th>
                            <th>Projet</th>
                            <th>Activité</th>
                            <th>DMO</th>
                            <th>Ex</th>
                            <th>Date de création</th>
                            <th>Vérification</th>
                            <th>#</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for package in BE_1 %}
                            <tr package-id="{{ package.id }}" link="{{ path('verif_package', {'id': package.id}) }}">
                                <td>{{ package.id }}</td>
                                <td>{{ package.owner }}</td>
                                <td>{{ package.getProjectRelation is not null and package.getProjectRelation.getOTP is not null ? package.getProjectRelation.getOTP : '' }}</td>
                                <td>{{ package.activity }}</td>
                                <td>
                                    {% set dmoCount = package.dmos|length %}
                                    {% if dmoCount > 2 %}
                                        <div class="dropdown">
                                            <span class="badge bg-secondary dropdown-toggle"
                                                id="dropdownMenuButton{{ package.id }}"
                                                data-bs-toggle="dropdown"
                                                aria-expanded="false">
                                                {{ dmoCount }} DMO
                                            </span>
                                            <ul class="dropdown-menu text-center shadow"
                                                aria-labelledby="dropdownMenuButton{{ package.id }}">
                                                {% for dmo in package.dmos %}
                                                    <li>
                                                        <a href="{{ path('app_dmo_show', {'id': dmo.getId()}) }}"
                                                           target="_blank"
                                                           class="badge bg-primary badge-hover"
                                                           style="text-decoration: none;">
                                                            {{ dmo.getDmoId() }}
                                                        </a>
                                                    </li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                    {% else %}
                                        {% for dmo in package.dmos %}
                                            <a href="{{ path('app_dmo_show', {'id': dmo.getId()}) }}"
                                               target="_blank"
                                               class="badge bg-primary badge-hover"
                                               style="text-decoration: none;">
                                                {{ dmo.getDmoId() }}
                                            </a>
                                        {% endfor %}
                                    {% endif %}
                                </td>
                                <td>{{ package.ex }}</td>
                                <td>
                                    {% if package.DateBE0 is not null %}
                                        {{ package.DateBE0|date('d/m/Y') }}
                                    {% endif %}
                                </td>
                                <td>{{ package.verif }}</td>
                                <td>{{ package.documents|length }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% endif %}
        </div>
        
        <!-- Package Validation -->
        <div class="tab-pane fade" id="validation" role="tabpanel">
            {% if BE|length == 0 %}
                <div class="alert alert-warning mt-2 mx-5" role="alert">
                    Aucun package à valider
                </div>
            {% else %}
                <table class="table table-hover table-sm table-bordered">
                    <thead>
                        <tr>
                            <th>N°</th>
                            <th>Propriétaire</th>
                            <th>Validation</th>
                            <th>Projet</th>
                            <th>Activité</th>
                            <th>DMO</th>
                            <th>Ex</th>
                            <th>Date de création</th>
                            <th>Date de vérif.</th>
                            <th>#</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for package in BE %}
                            <tr package-id="{{ package.id }}" link="{{ path('valid_package', {'id': package.id}) }}">
                                <td>{{ package.id }}</td>
                                <td>{{ package.owner }}</td>
                                <td>{{ package.valid }}</td>
                                <td>{{ package.getProjectRelation is not null and package.getProjectRelation.getOTP is not null ? package.getProjectRelation.getOTP : '' }}</td>
                                <td>{{ package.activity }}</td>
                                <td>
                                    {% set dmoCount = package.dmos|length %}
                                    {% if dmoCount > 2 %}
                                        <div class="dropdown">
                                            <span class="badge bg-secondary dropdown-toggle"
                                                id="dropdownMenuButton{{ package.id }}"
                                                data-bs-toggle="dropdown"
                                                aria-expanded="false">
                                                {{ dmoCount }} DMO
                                            </span>
                                            <ul class="dropdown-menu text-center shadow"
                                                aria-labelledby="dropdownMenuButton{{ package.id }}">
                                                {% for dmo in package.dmos %}
                                                    <li>
                                                        <a href="{{ path('app_dmo_show', {'id': dmo.getId()}) }}"
                                                           target="_blank"
                                                           class="badge bg-primary badge-hover"
                                                           style="text-decoration: none;">
                                                            {{ dmo.getDmoId() }}
                                                        </a>
                                                    </li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                    {% else %}
                                        {% for dmo in package.dmos %}
                                            <a href="{{ path('app_dmo_show', {'id': dmo.getId()}) }}"
                                               target="_blank"
                                               class="badge bg-primary badge-hover"
                                               style="text-decoration: none;">
                                                {{ dmo.getDmoId() }}
                                            </a>
                                        {% endfor %}
                                    {% endif %}
                                </td>
                                <td>{{ package.ex }}</td>
                                <td>
                                    {% if package.DateBE0 is not null %}
                                        {{ package.DateBE0|date('d/m/Y') }}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if package.DateBE1 is not null %}
                                        {{ package.DateBE1|date('d/m/Y') }}
                                    {% endif %}
                                </td>
                                <td>{{ package.documents|length }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal de création du package -->
<div class="modal fade" id="create-package" tabindex="-1" role="dialog" aria-labelledby="createPackageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createPackageModalLabel">Créer un package</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="create-package-form" method="POST" action="{{ path('create_package') }}">
                    <div class="row mx-0">
                        <div class="col-md-6">
                            <div class="form-group mt-2">
                                <label for="activity">Activité</label>
                                <select class="selectpicker" data-style="border btn" data-width="100%" id="activity" name="activity" required>
                                    <option value="OSI">OSI</option>
                                    <option value="MOB_INDUS">MOB_INDUS</option>
                                    <option value="MOB_AERO">MOB_AERO</option>
                                    <option value="METHOD">METHOD</option>
                                    <option value="ENERGY_SIGNAL">ENERGY_SIGNAL</option>
                                    <option value="ENERGY_RENEW">ENERGY_RENEW</option>
                                    <option value="ENERGY_POWER">ENERGY_POWER</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mt-2">
                                <label for="ex">Ex</label>
                                <select class="selectpicker" data-style="border btn" data-width="100%" id="ex" name="ex" required>
                                    <option value="NO">NO</option>
                                    <option value="IECEX">IECEX</option>
                                    <option value="CSA">CSA</option>
                                    <option value="ATEX">ATEX</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row mx-0">
                        <div class="col-md-6">
                            <!-- Select DMO dans le formulaire de création -->
                            <div class="form-group mt-2">
                                <label for="dmo">DMO</label>
                                <select class="selectpicker"
                                        id="dmo"
                                        name="dmo[]"
                                        required
                                        multiple
                                        data-style="border btn"
                                        data-width="100%"
                                        data-live-search="true"
                                        data-size="10"
                                        title="DMO">
                                    {% for otp, dmoList in dmobyProjects %}
                                        <optgroup label="{{ otp }}">
                                            {% for dmo in dmoList %}
                                                <option value="{{ dmo.id }}" data-project="{{ dmo.projectId }}" data-otp="{{ otp }}">
                                                    {{ dmo.id_dmo }}
                                                </option>
                                            {% endfor %}
                                        </optgroup>
                                    {% endfor %}

                                </select>
                            </div>

                        </div>
                        <div class="col-md-6">
                            <div class="form-group mt-2">
                                <label for="project">Projet</label>
                                <select class="selectpicker" data-style="border btn" data-width="100%" id="project" name="project" required data-live-search="true" data-size="10">
                                    {% for project in projects %}
                                        <option value="{{ project.id }}">{{ project.otp }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row mx-0">
                        <div class="col-md-12">
                            <div class="form-group mt-2">
                                <label for="description">Description</label>
                                <textarea class="form-control" data-style="border btn" data-width="100%"
                                          id="description" name="description" rows="3"
                                          required></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="creer-package" onclick="createPackage()">Créer</button>
            </div>
        </div>
    </div>
</div>

<script>

function handleDMOSelection($select) {
    const selectedOptions = $select.find('option:selected');
    
    if (selectedOptions.length > 0) {
        // On désactive le select project
        $('#project').empty();
        $('#project').prop('disabled', true);
        // On ajoute une option avec la valeur (id) et le label (otp)
        $('#project').append('<option value="' + selectedOptions.first().data('project') + '">' + selectedOptions.first().data('otp') + '</option>');
        $('#project').selectpicker('destroy');
        $('#project').selectpicker();
        
        // Désactive les options qui ne correspondent pas au projet du premier DMO sélectionné
        const selectedProject = selectedOptions.first().data('project');
        $select.find('option').each(function() {
            const isMatchingProject = $(this).data('project') === selectedProject;
            $(this).prop('disabled', !isMatchingProject);
        });
    } else {
        // Réactive le select project
        $('#project').prop('disabled', false);
        $('#project').empty();
        $('#project').selectpicker('destroy');

        // Recharge toutes les options
        {% for project in projects %}
            $('#project').append('<option value="{{ project.id }}">{{ project.otp }}</option>');
        {% endfor %}
        
        $('#project').selectpicker();
        // Réactive toutes les options DMO
        $select.find('option').each(function() {
            $(this).prop('disabled', false);
        });
    }
    // Réinitialise le select dmo pour refléter les changements
    $select.selectpicker('destroy');
    $select.selectpicker();
    $select.selectpicker('toggle');
}


    $(document).on('change', '#dmo', function() {
        handleDMOSelection($(this));
    });


    function createPackage() {
        $('#project').prop('disabled', false);
        $('#create-package-form').submit();
    }

    // Si on clique sur une ligne de tableau (hors "badge"), on redirige vers la page d'édition
    $(document).on('click', 'tbody>tr', function(e){
        if (!$(e.target).hasClass('badge') && !$(e.target).closest('.dropdown-menu').length) {
            window.location.href = $(this).attr('link');
        }
    });

    // Gère l'ouverture d'un onglet particulier via l'URL (#?onlget=BE_0&document-id=xxx)
    $(document).ready(function() {
        var hash = window.location.hash;
        if (!hash) return;

        var params = new URLSearchParams(hash.substring(1)); // Supprime le "#"
        var onlget = params.get('onlget');
        var packageId = params.get('document-id');

        var tabMap = { 'BE_0': 'update', 'BE_1': 'verification', 'BE': 'validation' },
            tabId = tabMap[onlget];

        if (!tabId) return;

        var tabTrigger = $('[data-bs-target="#' + tabId + '"]');
        if (!tabTrigger.length) return;

        // Active l’onglet correspondant
        new bootstrap.Tab(tabTrigger[0]).show();

        setTimeout(() => {
            let row = $('tr[package-id="' + packageId + '"]');
            if (row.length) {
                row[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                let blinkCount = 2; // Nombre de clignotements
                let interval = 1000; // Intervalle entre les clignotements
                let i = 0;
                const blink = () => {
                    row.toggleClass('row-highlight');
                    if (i < blinkCount * 2) { 
                        i++;
                        setTimeout(blink, interval);
                    }
                };
                blink();
            }
        }, 300);

    });
</script>


{# Définition d'une variable JS pour savoir si l'utilisateur est manager #}
<script>
    var isManager = {{ app.user.isManager|json_encode|raw }};
</script>

<style>
    /* Styles pour le menu contextuel personnalisé */
    #custom-context-menu {
        position: absolute;
        display: none;
        background-color: #fff;
        border: 1px solid #ccc;
        box-shadow: 0 2px 5px rgba(0,0,0,0.5);
        z-index: 1000;
        width: 200px;
    }
    #custom-context-menu ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    #custom-context-menu li {
        padding: 8px 12px;
        cursor: pointer;
    }
    #custom-context-menu li:hover {
        background-color: #f0f0f0;
    }
</style>

<div id="custom-context-menu">
    <ul>
        <li id="delete-document">Supprimer le package</li>
    </ul>
</div>

<script>
    // Pour les packages, seuls les utilisateurs NON managers doivent pouvoir supprimer.
    // Si l'utilisateur est manager, on ne surcharge pas le clic droit.
    if (isManager) {
        // On attache l'événement à toutes les lignes de tableau possédant l'attribut "package-id"
        $(document).on('contextmenu', 'tr[package-id]', function(e) {
            e.preventDefault(); // Empêche l'affichage du menu contextuel par défaut
            var packageId = $(this).attr('package-id');
            // Stocke l'id dans le menu et le positionne à l'endroit du clic
            $('#custom-context-menu').data('packageId', packageId).css({
                top: e.pageY + 'px',
                left: e.pageX + 'px',
                display: 'block'
            });
        });
    
        // Masquer le menu lorsqu'un clic se produit ailleurs
        $(document).on('click', function() {
            $('#custom-context-menu').hide();
        });
    
        // Au clic sur l'option "Supprimer le package", on demande une confirmation avant la suppression
        $('#delete-document').on('click', function() {
            Swal.fire({
                title: 'Confirmer suppression',
                text: "Êtes-vous sûr de vouloir supprimer ce package et tous ses documents ?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Oui, supprimer!',
                cancelButtonText: 'Annuler'
            }).then((result) => {
                if (result.isConfirmed) {
                    var packageId = $('#custom-context-menu').data('packageId');
                    // On récupère d'abord la ligne à supprimer
                    var $row = $('tr[package-id="' + packageId + '"]');
                    // On détermine dans quel onglet se trouve la ligne
                    var tabPaneId = $row.closest('.tab-pane').attr('id');
                    
                    $.ajax({
                        url: "{{ path('delete_package') }}",
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ id: packageId }),
                        success: function(response) {
                            if (response.status === 'success') {
                                // Supprimer la ligne correspondante du tableau
                                $row.remove();
                                
                                // Mettre à jour le compteur dans le badge de l'onglet concerné
                                var count = $('#' + tabPaneId + ' tbody tr').length;
                                // On cible le bouton de l'onglet avec data-bs-target correspondant
                                $('[data-bs-target="#' + tabPaneId + '"] .badge').text(count);
                                
                                Toast.fire({
                                    icon: 'success',
                                    title: 'Package supprimé avec succès'
                                });
                            } else {
                                Toast.fire({
                                    icon: 'error',
                                    title: 'Erreur : ' + response.message
                                });
                            }
                        },
                        error: function() {
                            Toast.fire({
                                icon: 'error',
                                title: 'Erreur lors de la suppression du package'
                            });
                        }
                    });
                }
            });
        });
    }
</script>


{% endblock %}
