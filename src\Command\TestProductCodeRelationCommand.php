<?php

namespace App\Command;

use App\Entity\ProductCode;
use App\Repository\ProductCodeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:test-product-code-relation',
    description: 'Teste les relations ProductCode'
)]
class TestProductCodeRelationCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $em,
        private ProductCodeRepository $productCodeRepository
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Test des relations ProductCode...</info>');

        // 1. Tester la récupération des ProductCode
        $productCodes = $this->productCodeRepository->findAll();
        $output->writeln('<info>ProductCode trouvés: ' . count($productCodes) . '</info>');

        // 2. Tester la méthode findByCode
        $testCode = '9316';
        $productCode = $this->productCodeRepository->findByCode($testCode);
        if ($productCode) {
            $output->writeln('<info>ProductCode trouvé pour le code "' . $testCode . '": ' . $productCode->getId() . '</info>');
        } else {
            $output->writeln('<error>ProductCode non trouvé pour le code "' . $testCode . '"</error>');
        }

        // 3. Tester la méthode findOrCreateByCode avec un code existant
        $existingCode = '9316';
        $productCode = $this->productCodeRepository->findOrCreateByCode($existingCode, true);
        $output->writeln('<info>findOrCreateByCode pour code existant "' . $existingCode . '": ID ' . $productCode->getId() . '</info>');

        // 4. Tester la méthode findOrCreateByCode avec un nouveau code
        $newCode = 'TEST_CODE_' . time();
        $productCode = $this->productCodeRepository->findOrCreateByCode($newCode, false);
        $output->writeln('<info>findOrCreateByCode pour nouveau code "' . $newCode . '": ID ' . $productCode->getId() . ', etat: ' . ($productCode->isEtat() ? 'true' : 'false') . '</info>');

        // 5. Vérifier que le nouveau code a bien été créé
        $verifyCode = $this->productCodeRepository->findByCode($newCode);
        if ($verifyCode) {
            $output->writeln('<info>Vérification: nouveau code créé avec succès</info>');
        } else {
            $output->writeln('<error>Erreur: nouveau code non trouvé après création</error>');
        }

        // 6. Tester les ProductCode actifs et inactifs
        $activeCount = count($this->productCodeRepository->findActive());
        $inactiveCount = count($this->productCodeRepository->findInactive());
        $output->writeln('<info>ProductCode actifs: ' . $activeCount . '</info>');
        $output->writeln('<info>ProductCode inactifs: ' . $inactiveCount . '</info>');

        // 7. Afficher quelques exemples
        $output->writeln('<comment>Exemples de ProductCode:</comment>');
        $examples = $this->productCodeRepository->findBy([], ['code' => 'ASC'], 5);
        foreach ($examples as $example) {
            $output->writeln('<comment>  - ' . $example->getCode() . ' (ID: ' . $example->getId() . ', etat: ' . ($example->isEtat() ? 'actif' : 'inactif') . ')</comment>');
        }

        $output->writeln('<info>Test terminé avec succès !</info>');
        return Command::SUCCESS;
    }
}
