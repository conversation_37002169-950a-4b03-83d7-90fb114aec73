<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250616080354 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE material (id INT AUTO_INCREMENT NOT NULL, reference VARCHAR(255) NOT NULL, description VARCHAR(255) NOT NULL, thickness_min INT DEFAULT NULL, thickness_max INT DEFAULT NULL, thickness_unit VARCHAR(50) DEFAULT NULL, density INT DEFAULT NULL, status VARCHAR(50) NOT NULL, rohs VARCHAR(255) DEFAULT NULL, reach VARCHAR(255) DEFAULT NULL, legacy_id INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE document ADD material_entity_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE document ADD CONSTRAINT FK_D8698A76B9BB67BD FOREIGN KEY (material_entity_id) REFERENCES material (id)');
        $this->addSql('CREATE INDEX IDX_D8698A76B9BB67BD ON document (material_entity_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE document DROP FOREIGN KEY FK_D8698A76B9BB67BD');
        $this->addSql('DROP TABLE material');
        $this->addSql('DROP INDEX IDX_D8698A76B9BB67BD ON document');
        $this->addSql('ALTER TABLE document DROP material_entity_id');
    }
}
