<?php

namespace App\Command;

use App\Entity\Document;
use App\Entity\Material;
use App\Repository\DocumentRepository;
use App\Repository\MaterialRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:test-material-relation',
    description: 'Teste la relation Material dans l\'entité Document'
)]
class TestMaterialRelationCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $em,
        private MaterialRepository $materialRepository,
        private DocumentRepository $documentRepository
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Test de la relation Material');

        try {
            // Test 1: Récupérer quelques matériaux
            $io->section('1. Liste des premiers matériaux');
            $materials = $this->materialRepository->findBy([], ['reference' => 'ASC'], 5);
            foreach ($materials as $material) {
                $io->writeln("   - {$material->getReference()}: {$material->getDescription()}");
            }

            // Test 2: Récupérer le document avec matériau
            $io->section('2. Document avec matériau');
            $document = $this->documentRepository->find(222528);
            if ($document) {
                $io->writeln("   Document ID: {$document->getId()}");
                $io->writeln("   Référence: {$document->getReference()}");
                $io->writeln("   Matériau (méthode de compatibilité): " . ($document->getMaterial() ?: 'Aucun'));
                
                $materialEntity = $document->getMaterialEntity();
                if ($materialEntity) {
                    $io->writeln("   Matériau entité:");
                    $io->writeln("     - ID: {$materialEntity->getId()}");
                    $io->writeln("     - Référence: {$materialEntity->getReference()}");
                    $io->writeln("     - Description: {$materialEntity->getDescription()}");
                    $io->writeln("     - Statut: {$materialEntity->getStatus()}");
                } else {
                    $io->writeln("   Aucun matériau lié");
                }
            } else {
                $io->writeln("   Document non trouvé");
            }

            // Test 3: Test de la méthode toJson()
            $io->section('3. Test de la sérialisation JSON');
            if ($document) {
                $json = $document->toJson();
                if (isset($json['material'])) {
                    $io->writeln("   Matériau dans JSON:");
                    $io->writeln("     - ID: " . ($json['material']['id'] ?? 'N/A'));
                    $io->writeln("     - Référence: " . ($json['material']['reference'] ?? 'N/A'));
                    $io->writeln("     - Description: " . ($json['material']['description'] ?? 'N/A'));
                } else {
                    $io->writeln("   Aucun matériau dans JSON");
                }
            }

            // Test 4: Recherche de matériaux
            $io->section('4. Recherche de matériaux (FMME)');
            $searchResults = $this->materialRepository->search('FMME');
            $io->writeln("   Trouvé " . count($searchResults) . " matériaux contenant 'FMME'");
            foreach (array_slice($searchResults, 0, 3) as $material) {
                $io->writeln("   - {$material->getReference()}: {$material->getDescription()}");
            }

            // Test 5: Matériaux actifs
            $io->section('5. Nombre de matériaux actifs');
            $activeMaterials = $this->materialRepository->findActive();
            $io->writeln("   " . count($activeMaterials) . " matériaux actifs trouvés");

            // Test 6: Test de création d'un nouveau document avec matériau
            $io->section('6. Test de création d\'un document avec matériau');
            $testMaterial = $this->materialRepository->findByReference('FMME 1001');
            if ($testMaterial) {
                $io->writeln("   Matériau de test trouvé: {$testMaterial->getReference()}");
                $io->writeln("   Nombre de documents liés: " . $testMaterial->getDocuments()->count());
            }

            $io->success('Tests terminés avec succès !');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $io->error('Erreur lors des tests : ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
