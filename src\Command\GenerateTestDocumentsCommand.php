<?php

namespace App\Command;

use App\Entity\Document;
use App\Entity\User;
use App\Entity\Visa;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:generate-test-documents',
    description: 'Génère des documents de test avec des temps de traitement variés pour tester les prédictions',
)]
class GenerateTestDocumentsCommand extends Command
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
        $this
            ->addArgument('count', InputArgument::OPTIONAL, 'Nombre de documents à générer', 50)
            ->addArgument('admin_id', InputArgument::OPTIONAL, 'ID de l\'utilisateur admin à utiliser comme superviseur', 1);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $count = $input->getArgument('count');
        $adminId = $input->getArgument('admin_id');

        $io->title('Génération de documents de test');
        $io->progressStart($count);

        // Récupérer l'utilisateur admin
        $admin = $this->entityManager->getRepository(User::class)->find($adminId);
        if (!$admin) {
            $io->error("Utilisateur avec l'ID $adminId non trouvé. Utilisez un ID d'utilisateur valide.");
            return Command::FAILURE;
        }

        // Types de documents possibles
        $docTypes = ['ASSY', 'MACH', 'MOLD', 'DOC', 'PUR'];
        $procTypes = ['E', 'F', 'K', 'Z'];
        $materialTypes = ['HALB', 'FERT', 'ROH', 'NLAG'];

        // Calculer combien de documents de chaque type nous devons créer
        $docsPerType = floor($count / count($docTypes));
        $remainingDocs = $count % count($docTypes);

        $typeDistribution = [];
        foreach ($docTypes as $type) {
            $typeDistribution[$type] = $docsPerType;
        }

        // Distribuer les documents restants
        for ($i = 0; $i < $remainingDocs; $i++) {
            $typeDistribution[$docTypes[$i]]++;
        }

        $io->section('Distribution des types de documents');
        foreach ($typeDistribution as $type => $number) {
            $io->writeln("$type: $number documents");
        }

        // Compteur global pour les références
        $globalCounter = 1;

        // Générer les documents pour chaque type
        foreach ($typeDistribution as $docType => $typeCount) {
            $io->section("Génération de $typeCount documents de type $docType");

            for ($i = 1; $i <= $typeCount; $i++) {
                $document = new Document();

                // Générer un numéro de référence unique
                $reference = sprintf('TEST-%05d', $globalCounter);

                // Définir les propriétés de base
                $document->setReference($reference);
                $document->setRefRev('A');
                $document->setRefTitleFra("Document $docType de test " . $i);
                $document->setProdDraw('PROD-' . $reference);
                $document->setProdDrawRev('1');
                $document->setAlias('ALIAS-' . $reference);

                // Définir le type de document
                $document->setDocType($docType);

                // Attribuer un type de processus approprié pour ce type de document
                if ($docType == 'ASSY' || $docType == 'MACH') {
                    $procType = 'E'; // Processus standard pour ASSY et MACH
                } elseif ($docType == 'MOLD') {
                    $procType = 'F'; // Processus spécifique pour MOLD
                } else {
                    $procType = $procTypes[array_rand($procTypes)]; // Aléatoire pour les autres
                }
                $document->setProcType($procType);

                // Attribuer un type de matériau approprié pour ce type de document
                if ($docType == 'ASSY') {
                    $matProdType = 'FERT'; // Produit fini pour ASSY
                } elseif ($docType == 'MACH' || $docType == 'MOLD') {
                    $matProdType = 'HALB'; // Semi-fini pour MACH et MOLD
                } else {
                    $matProdType = $materialTypes[array_rand($materialTypes)]; // Aléatoire pour les autres
                }
                $document->setMatProdType($matProdType);

                // Définir d'autres propriétés importantes
                $document->setMaterial('MAT-' . $reference);
                $document->setEx('EX-' . $reference);
                $document->setAction('CREATE');
                $document->setHts('HTS-' . $reference);
                $document->setEccn('ECCN-' . $reference);
                $document->setRdo('RDO-' . $reference);

                // Définir des propriétés spécifiques au type de document
                if ($docType == 'ASSY') {
                    $document->setUnit('PC');
                } elseif ($docType == 'MACH') {
                    $document->setUnit('PC Prod Sup.: USI');
                } elseif ($docType == 'MOLD') {
                    $document->setUnit('PC');
                } else {
                    $document->setUnit('PC');
                }

                // matProdType déjà défini plus haut
                $document->setDocImpact(true);
                $document->setSuperviseur($admin);

                // Simuler des états de workflow avec des timestamps
                $this->generateWorkflowHistory($document, $admin);

                // Ajouter une entrée dans l'historique des mises à jour
                $document->addUpdate('create', $admin, 'Création du document de test');

                // Persister le document
                $this->entityManager->persist($document);

                // Incrémenter le compteur global
                $globalCounter++;

                // Flush tous les 20 documents pour éviter de surcharger la mémoire
                if ($globalCounter % 20 === 0) {
                    $this->entityManager->flush();
                    $io->progressAdvance(20);
                }
            }
        }

        // Flush final
        $this->entityManager->flush();
        $io->progressFinish();

        $io->success("$count documents de test ont été générés avec succès.");

        return Command::SUCCESS;
    }

    /**
     * Génère un historique de workflow réaliste pour un document
     */
    private function generateWorkflowHistory(Document $document, User $admin): void
    {
        // États possibles du workflow (basés sur le fichier workflow.yaml)
        $states = [
            'BE_0' => 'BE_0',
            'BE_1' => 'BE_1',
            'BE' => 'BE',
            'Produit' => 'Produit',
            'Quality' => 'Quality',
            'Assembly' => 'Assembly',
            'Machining' => 'Machining',
            'Molding' => 'Molding',
            'Core_Data' => 'Core_Data',
            'Prod_Data' => 'Prod_Data',
            'GID' => 'GID',
            'Costing' => 'Costing',
            'Released' => 'Released'
        ];

        // Déterminer si le document est terminé ou en cours
        $isCompleted = (rand(0, 100) < 70); // 70% de chance d'être terminé

        // Initialiser les timestamps
        $stateTimestamps = [];

        // Date de départ (entre 1 et 60 mois dans le passé pour couvrir 5 ans)
        $startDate = new \DateTime();

        // Répartir les documents de manière plus uniforme sur 5 ans
        // On divise la plage de 60 mois en segments et on attribue un segment à chaque document
        // en fonction de son ID pour assurer une bonne répartition
        $documentId = $document->getId() ?? 0;
        $monthsAgo = 1 + ($documentId % 60); // Entre 1 et 60 mois dans le passé

        $startDate->modify('-' . $monthsAgo . ' months');

        // Ajouter une variation aléatoire de quelques jours pour éviter que tous les documents
        // d'un même mois commencent exactement le même jour
        $startDate->modify('+' . rand(1, 28) . ' days');

        // Pour chaque état, générer des timestamps
        $currentDate = clone $startDate;

        // Définir le chemin du workflow en fonction du type de document
        $docType = $document->getDocType();
        $workflowPath = $this->getWorkflowPathForDocType($docType, $isCompleted);

        // Générer les timestamps pour chaque état du chemin
        foreach ($workflowPath as $index => $stateKey) {
            $state = $states[$stateKey];

            // Ajouter l'entrée dans l'état
            $enterDate = clone $currentDate;

            // Déterminer combien de temps le document reste dans cet état (entre 1 et 20 jours)
            $daysInState = rand(1, 20);
            $currentDate->modify("+$daysInState days");

            // Ajouter la sortie de l'état si ce n'est pas le dernier état
            $exitDate = null;
            $toState = null;
            if ($index < count($workflowPath) - 1) {
                $exitDate = clone $currentDate;
                $toState = $states[$workflowPath[$index + 1]];
            }

            // Stocker les timestamps
            if (!isset($stateTimestamps[$state])) {
                $stateTimestamps[$state] = [];
            }

            $entry = [
                'enter' => $enterDate->format('Y-m-d H:i:s'),
                'user_id' => $admin->getId(),
                'exit' => null,
                'to_state' => null,
            ];

            if ($exitDate) {
                $entry['exit'] = $exitDate->format('Y-m-d H:i:s');
                $entry['to_state'] = $toState;
            }

            $stateTimestamps[$state][] = $entry;
        }

        // Définir l'état actuel comme le dernier état du chemin
        $finalState = $states[$workflowPath[count($workflowPath) - 1]];
        $currentSteps = [$finalState => true];

        $document->setCurrentSteps($currentSteps);
        $document->setStateTimestamps($stateTimestamps);

        // Si le document est terminé, ajouter les visas appropriés
        if ($isCompleted) {
            $this->addVisasForCompletedDocument($document, $admin, $finalState);
        }
    }

    /**
     * Retourne un chemin de workflow en fonction du type de document
     */
    private function getWorkflowPathForDocType(string $docType, bool $isCompleted): array
    {
        // Chemins de base pour chaque type de document
        $basePaths = [
            'ASSY' => ['BE_0', 'BE_1', 'BE', 'Produit', 'Assembly'],
            'MACH' => ['BE_0', 'BE_1', 'BE', 'Produit', 'Machining'],
            'MOLD' => ['BE_0', 'BE_1', 'BE', 'Produit', 'Molding'],
            'DOC' => ['BE_0', 'BE_1', 'BE', 'Quality'],
            'PUR' => ['BE_0', 'BE_1', 'BE', 'Produit']
        ];

        // Si le document n'est pas terminé, retourner un chemin partiel
        if (!$isCompleted) {
            $path = $basePaths[$docType] ?? $basePaths['DOC'];
            // Prendre une partie aléatoire du chemin (au moins 2 états)
            $length = rand(2, count($path));
            return array_slice($path, 0, $length);
        }

        // Pour les documents terminés, ajouter des états supplémentaires
        $completedPaths = [
            'ASSY' => ['BE_0', 'BE_1', 'BE', 'Produit', 'Assembly', 'Quality', 'Core_Data', 'Prod_Data', 'GID', 'Costing'],
            'MACH' => ['BE_0', 'BE_1', 'BE', 'Produit', 'Machining', 'Quality', 'Core_Data', 'Prod_Data', 'GID', 'Costing'],
            'MOLD' => ['BE_0', 'BE_1', 'BE', 'Produit', 'Molding', 'Quality', 'Core_Data', 'Prod_Data', 'GID', 'Costing'],
            'DOC' => ['BE_0', 'BE_1', 'BE', 'Quality', 'Core_Data', 'Prod_Data', 'GID'],
            'PUR' => ['BE_0', 'BE_1', 'BE', 'Produit', 'Quality', 'Core_Data', 'Prod_Data']
        ];

        return $completedPaths[$docType] ?? $completedPaths['DOC'];
    }

    /**
     * Ajoute les visas appropriés pour un document terminé
     */
    private function addVisasForCompletedDocument(Document $document, User $admin, string $finalState): void
    {
        // Créer un visa pour l'état final
        $visa = new Visa();
        $visa->setReleasedDrawing($document);
        $visa->setName('visa_' . $finalState);
        $visa->setStatus('valid');
        $visa->setDateVisa(new \DateTimeImmutable());
        $visa->setValidator($admin);

        $this->entityManager->persist($visa);

        // Ajouter des visas pour les états précédents si nécessaire
        $previousStates = $this->getPreviousStatesForVisa($finalState);
        foreach ($previousStates as $state) {
            $visa = new Visa();
            $visa->setReleasedDrawing($document);
            $visa->setName('visa_' . $state);
            $visa->setStatus('valid');
            $visa->setDateVisa(new \DateTimeImmutable());
            $visa->setValidator($admin);

            $this->entityManager->persist($visa);
        }
    }

    /**
     * Retourne les états précédents pour lesquels des visas sont nécessaires
     */
    private function getPreviousStatesForVisa(string $finalState): array
    {
        $visaMap = [
            'BE_0' => [],
            'BE_1' => ['BE_0'],
            'BE' => ['BE_0', 'BE_1'],
            'Produit' => ['BE'],
            'Quality' => ['BE'],
            'Assembly' => ['Produit'],
            'Machining' => ['Produit'],
            'Molding' => ['Produit'],
            'Core_Data' => ['Quality'],
            'Prod_Data' => ['Core_Data'],
            'GID' => ['Prod_Data'],
            'Costing' => ['GID']
        ];

        return $visaMap[$finalState] ?? [];
    }
}
