<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250616141530 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE product_code (id INT AUTO_INCREMENT NOT NULL, code VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, family VARCHAR(255) DEFAULT NULL, subfamily VARCHAR(255) DEFAULT NULL, type VARCHAR(255) DEFAULT NULL, subtype VARCHAR(255) DEFAULT NULL, category VARCHAR(255) DEFAULT NULL, subcategory VARCHAR(255) DEFAULT NULL, etat TINYINT(1) DEFAULT 1 NOT NULL, legacy_id INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE document ADD product_code_id INT DEFAULT NULL, DROP product_code');
        $this->addSql('ALTER TABLE document ADD CONSTRAINT FK_D8698A76F603E5A7 FOREIGN KEY (product_code_id) REFERENCES product_code (id)');
        $this->addSql('CREATE INDEX IDX_D8698A76F603E5A7 ON document (product_code_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE document DROP FOREIGN KEY FK_D8698A76F603E5A7');
        $this->addSql('DROP TABLE product_code');
        $this->addSql('DROP INDEX IDX_D8698A76F603E5A7 ON document');
        $this->addSql('ALTER TABLE document ADD product_code VARCHAR(255) DEFAULT NULL, DROP product_code_id');
    }
}
