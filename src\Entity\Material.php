<?php

namespace App\Entity;

use App\Repository\MaterialRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: MaterialRepository::class)]
class Material
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $reference = null;

    #[ORM\Column(length: 255)]
    private ?string $description = null;

    #[ORM\Column(nullable: true)]
    private ?int $thicknessMin = null;

    #[ORM\Column(nullable: true)]
    private ?int $thicknessMax = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $thicknessUnit = null;

    #[ORM\Column(nullable: true)]
    private ?int $density = null;

    #[ORM\Column(length: 50)]
    private ?string $status = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $rohs = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $reach = null;

    #[ORM\Column(nullable: true)]
    private ?int $legacyId = null;

    /**
     * @var Collection<int, Document>
     */
    #[ORM\ManyToMany(targetEntity: Document::class, mappedBy: 'materials')]
    private Collection $documents;

    public function __construct()
    {
        $this->documents = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getReference(): ?string
    {
        return $this->reference;
    }

    public function setReference(string $reference): static
    {
        $this->reference = $reference;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): static
    {
        $this->description = $description;
        return $this;
    }

    public function getThicknessMin(): ?int
    {
        return $this->thicknessMin;
    }

    public function setThicknessMin(?int $thicknessMin): static
    {
        $this->thicknessMin = $thicknessMin;
        return $this;
    }

    public function getThicknessMax(): ?int
    {
        return $this->thicknessMax;
    }

    public function setThicknessMax(?int $thicknessMax): static
    {
        $this->thicknessMax = $thicknessMax;
        return $this;
    }

    public function getThicknessUnit(): ?string
    {
        return $this->thicknessUnit;
    }

    public function setThicknessUnit(?string $thicknessUnit): static
    {
        $this->thicknessUnit = $thicknessUnit;
        return $this;
    }

    public function getDensity(): ?int
    {
        return $this->density;
    }

    public function setDensity(?int $density): static
    {
        $this->density = $density;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;
        return $this;
    }

    public function getRohs(): ?string
    {
        return $this->rohs;
    }

    public function setRohs(?string $rohs): static
    {
        $this->rohs = $rohs;
        return $this;
    }

    public function getReach(): ?string
    {
        return $this->reach;
    }

    public function setReach(?string $reach): static
    {
        $this->reach = $reach;
        return $this;
    }

    public function getLegacyId(): ?int
    {
        return $this->legacyId;
    }

    public function setLegacyId(?int $legacyId): static
    {
        $this->legacyId = $legacyId;
        return $this;
    }

    /**
     * @return Collection<int, Document>
     */
    public function getDocuments(): Collection
    {
        return $this->documents;
    }

    public function addDocument(Document $document): static
    {
        if (!$this->documents->contains($document)) {
            $this->documents->add($document);
            $document->addMaterial($this);
        }

        return $this;
    }

    public function removeDocument(Document $document): static
    {
        if ($this->documents->removeElement($document)) {
            $document->removeMaterial($this);
        }

        return $this;
    }

    public function __toString(): string
    {
        return $this->reference . ' - ' . $this->description;
    }
}
