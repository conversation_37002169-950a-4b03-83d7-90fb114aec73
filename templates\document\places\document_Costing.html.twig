{% extends 'base.html.twig' %}

{% block title %}Documents{% endblock %}

{% block body %}

<style>
#table-container{
    padding: 1rem;
    background-color: #F8F9FA;
    border-radius: 0.5rem;
}

#table tr:last-child {
    border: none!important;
}


.table-filter-input {
    width: 100%;
    font-size: 0.85rem;
    height: calc(1.8rem + 2px);
}

#table td span {
    cursor: pointer;
    transition: all 0.3s;
}
#table td span:hover {
    padding: 0.375rem 0.75rem;
}


#table th, #table td {
    vertical-align: middle!important;
    white-space: nowrap;
    text-align: center!important;
    padding: 0.15rem!important;
    border: none!important;
}

#table thead th {
    user-select: none;
}

#table thead tr{
    border: none;
}


#table thead tr#entetes th {
    background-color: #004080;
    color: #fff;
    font-size: 0.85rem;
}

#table thead tr#filtres th {
    background-color: #F8F9FA; /* gris clair */
    border: none;
    cursor: pointer;
}

/* Icônes de tri */
th.sort-asc i, th.sort-desc i {
    margin-left: 5px;
}

/* Badges */
.badge.bg-primary {
    background:  #0059B3!important;
}

/* Champs invalides */
.is-invalid {
    border-color: #dc3545;
}

/* Boutons */
.btn-refresh {
    margin-bottom: 1rem;
}

/* --- Modale personnalisée --- */
.modal-dialog.modal-lg {
    max-width: 900px;
}

.modal-content.custom-modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.modal-header.custom-modal-header {
    background-color: #004080;
    color: #fff;
    border-bottom: none;
}

.modal-header.custom-modal-header .btn-close {
    filter: invert(100%); /* Rendre la croix blanche sur fond bleu */
}

.modal-footer.custom-modal-footer {
    border-top: none;
}

.tooltip .tooltip-inner {
max-width: 400px;
overflow-y: auto;   /* Barre de défilement si dépasse */
white-space: normal; /* Permet de passer à la ligne */
}

/* Styles pour la pagination */
.pagination-container {
    margin: 20px 0;
}

.pagination-container .pagination {
    margin-bottom: 0;
}

.pagination-container .page-item.active .page-link {
    background-color: #004080;
    border-color: #004080;
}

.pagination-container .page-link {
    color: #004080;
}

.pagination-container .page-link:hover {
    color: #002040;
    background-color: #e9ecef;
}

/* Styles pour l'indicateur de progression */
#progressModal .modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

#progressModal .progress {
    border-radius: 12px;
    background-color: #e9ecef;
}

#progressModal .progress-bar {
    border-radius: 12px;
    font-weight: bold;
    transition: width 0.3s ease;
}

#progressModal .spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Styles pour le modal des commentaires */
.comment-item {
    background-color: #f8f9fa;
    border-left: 4px solid #009BFF;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.comment-item:last-child {
    margin-bottom: 0;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.comment-state {
    background-color: #009BFF;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.comment-meta {
    color: #6c757d;
    font-size: 0.9rem;
}

.comment-content {
    color: #333;
    line-height: 1.5;
    margin-top: 8px;
    padding: 10px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.no-comments {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 40px 20px;
}

.comments-count {
    background-color: #e9ecef;
    color: #495057;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    margin-bottom: 20px;
    display: inline-block;
}

/* Styles pour le formulaire d'ajout de commentaire */
.modal-footer.custom-modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 15px 20px;
}

#newCommentText {
    resize: vertical;
    min-height: 38px;
}

#addCommentBtn {
    background-color: #009BFF;
    border-color: #009BFF;
    height: 38px;
}

#addCommentBtn:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.comment-form-loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Retirer la transparence des tooltips pour une meilleure lisibilité */
.tooltip .tooltip-inner {
    background-color: rgba(0, 0, 0, 0.95) !important;
    opacity: 1 !important;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before,
.tooltip.bs-tooltip-bottom .tooltip-arrow::before,
.tooltip.bs-tooltip-start .tooltip-arrow::before,
.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-color: rgba(0, 0, 0, 0.95) transparent !important;
}

</style>

<div class="mt-3" style="margin: 0 2%">
    <div class="row">
        <div class="col">
            <h3 class="mb-2">Costing</h3>

           {% if documents is empty %}
                <div class="alert alert-warning" role="alert">
                    Aucun document trouvé.
                </div>
            {% else %}
                <div class="card shadow border-0">
                    <div class="card-body p-0">
                        <div id="table-container" >
                            <table class="table table-hover table-bordered mb-0" id="table">
                                <thead>
                                    <!-- Ligne de filtres -->
                                    <tr id="filtres">
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="#" data-col="0"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Pack" data-col="1"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Activité" data-col="2"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Référence" data-col="3"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="R" data-col="4"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="prod plan" data-col="5"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="R" data-col="6"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Titre" data-col="8"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Action" data-col="7"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Inventaire" data-col="8"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Type" data-col="9"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="CLS" data-col="12"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="MOQ" data-col="13"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="SAP Data" data-col="14"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Commentaires" data-col="18"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Étapes" data-col="19"></th>
                                        <th>
                                            <div class="d-flex flex-column">
                                                <span id="signer-mass" style="display: none" class="badge bg-success mb-1" onclick="signSelectedDocuments()"> Signer sélectionnés </span>
                                                <span id="select-all-pages" class="badge bg-info" onclick="selectAllDocumentsAcrossPages()"> Sélectionner tous </span>
                                            </div>
                                        </th>
                                        <th>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-secondary me-2">{{ total_documents }}{{ total_documents == 1 ? ' Document' : ' Documents' }}</span>
                                                <a href="{{ path('app_document_export_csv', {'place': place}) }}" class="badge bg-primary" title="Exporter tous les documents en CSV"><i class="fas fa-file-csv"></i> CSV</a>
                                            </div>
                                        </th>
                                    </tr>
                                    <!-- Ligne d'entêtes -->
                                    <tr id="entetes">
                                        <th>#</th>
                                        <th>Pack</th>
                                        <th>Activité</th>
                                        <th>Référence</th>
                                        <th>R</th>
                                        <th>prod plan</th>
                                        <th>R</th>
                                        <th>Titre</th>
                                        <th>Action</th>
                                        <th>Inventaire</th>
                                        <th>Type</th>
                                        <th>CLS</th>
                                        <th>MOQ</th>
                                        <th> SAP Data </th>
                                        <th>Commentaires</th>
                                        <th>Révisions</th>
                                        <th>Validations</th>
                                        <th>Visas</th>
                                    </tr>
                                </thead>
                                <tbody>
                                {% for document in documents %}
                                    <tr document-id="{{document.id}}">
                                        <td>
                                          {% if document.stateTimestamps is not null %}
                                            {% if document.stateTimestamps[place] is defined %}
                                                {% set arrivedDate = date(document.stateTimestamps[place]) %}
                                                {% set diff = date().diff(arrivedDate) %}
                                                <span class="badge bg-primary">{{ diff.days }} jour{{ diff.days > 1 ? 's' }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">N/A</span>
                                            {% endif %}

                                        {% else %}
                                            <span class="badge bg-secondary">N/A</span>
                                        {% endif %}
                                            {# {{document.getDaysInState(place)}} #}
                                        </td>
                                        <td><a href="{{ path('detail_package', {'id': document.relPack.id}) }}" class="badge bg-primary pack-link">{{document.relPack.id}}</a></td>
                                        <td>
                                            <table class="table table-striped table-bordered mb-0 text-center" style="font-size: 0.7rem;">
                                                <tbody>
                                                    <tr>
                                                        <td class="p-1" style="font-size: 0.70rem;"> {{ document.relPack.activity }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="p-1" style="font-size: 0.70rem;"> {{ document.relPack.getProjectRelation() ? document.relPack.getProjectRelation.otp() : '' }}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                        <td style ="font-size: 0.7rem;">{{ document.reference|trim }}</td>
                                        <td>
                                            {{ document.refrev }}
                                            {% if document.ex != 'NO' %}
                                                <span style="color: red; font-weight: 500;"><sup>{{ document.ex }}</sup></span>
                                            {% else %}
                                                <sup>{{ document.ex }}</sup>
                                            {% endif %}
                                        </td>
                                        <td class="p-1" style="font-size: 0.80rem;">
                                            <a href="https://app.aletiq.com/parts/preview/id/{{ document.prodDraw }}/revision/{{ document.prodDrawRev }}"
                                                target="_blank"
                                                class="badge bg-primary preview-tooltip">
                                                {{ document.prodDraw }}
                                            </a>
                                        </td>

                                        {# <td class="p-1" style="font-size: 0.80rem;"><a href="https://app.aletiq.com/parts/preview/id/{{ document.prodDraw }}/revision/{{ document.prodDrawRev }}" target="_blank" class="badge bg-primary">{{ document.prodDraw }}</a></td> #}
                                        <td>{{ document.prodDrawRev }}</td>
                                        <td style="font-size: 0.7rem;">{{ document.refTitleFra }}</td>
                                        <td>{{ document.action }}</td>
                                        <td>{{ document.getInventoryImpact }}</td>

                                        <td style="padding: 0!important;">
                                            <table class="table table-striped table-bordered mb-0 text-center" style="font-size: 0.7rem;">
                                                <tbody>
                                                    <tr>
                                                        <td style="font-size: 0.70rem;"><strong>doc Type</strong> {{ document.docType }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td style="font-size: 0.70rem;"><strong>Mat Type</strong> {{ document.matProdType }}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                        <td>{{ document.cls }}</td>
                                        <td>{{ document.moq }}</td>

                                        <td style="padding: 0!important;">
                                            <table class="table table-striped table-bordered mb-0 text-center" style="font-size: 0.7rem;">
                                                <tbody>
                                                <tr>
                                                    <td style="font-size: 0.70rem;"><strong>Val</strong></td>
                                                    <td style="font-size: 0.70rem;">
                                                        {% set class_val = '-' %}
                                                        {% if document.matProdType == 'FERT' %}
                                                            {% set class_val = '7920' %}
                                                        {% elseif document.matProdType == 'HALB' and (document.procType == 'F' or document.procType == 'E') %}
                                                            {% set class_val = '7900' %}
                                                        {% elseif document.matProdType == 'HALB' and document.procType == 'F30' %}
                                                            {% set class_val = '7910' %}
                                                        {% elseif document.matProdType == 'VERP' %}
                                                            {% set class_val = '3050' %}
                                                        {% elseif document.matProdType == 'ROH' %}
                                                            {% if document.docType == 'DOC' %}
                                                                {% set class_val = '3000' %}
                                                            {% else %}
                                                                {% set class_val = '3030' %}
                                                            {% endif %}
                                                        {% endif %}
                                                        {{ class_val }}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 0.70rem;"><strong>Prix</strong></td>
                                                    <td style="font-size: 0.70rem;">
                                                        {% set code_prix = '-' %}
                                                        {% if document.procType == 'F' or document.procType == 'F30' %}
                                                            {% set code_prix = 'V' %}
                                                        {% elseif document.procType == 'E' %}
                                                            {% set code_prix = 'S' %}
                                                        {% endif %}
                                                        {{ code_prix }}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 0.70rem;"><strong>Frais</strong></td>
                                                    <td style="font-size: 0.70rem;">
                                                        {% set frai_gen = '-' %}
                                                        {% if document.matProdType != 'FERT' and (document.procType == 'F' or document.procType == 'F30') %}
                                                            {% set frai_gen = 'ROH' %}
                                                        {% elseif document.matProdType != 'VERP' or document.matProdType != 'ROH' %}
                                                            {% set frai_gen = 'ROH' %}
                                                        {% endif %}
                                                        {{ frai_gen }}
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                       <td>
                                            {% if document.commentaires|length > 0 %}
                                                <i class="fas fa-file-alt"
                                                style="color: #009BFF; cursor: pointer;"
                                                data-bs-toggle="tooltip"
                                                data-document-id="{{ document.id }}"
                                                onmouseenter="loadCommentsTooltip(this)"
                                                onclick="showCommentsModal({{ document.id }}, '{{ document.reference|escape('js') }}')"
                                                title="Cliquer pour voir les commentaires ({{ document.commentaires|length }}) | Survoler pour aperçu"
                                                txt="{% for comment in document.commentaires %}
                                                <strong>{{ comment.state|upper|e }}</strong> : {{ comment.commentaire|e }}
                                                par <em>{{ comment.user|e }}</em><br>
                                                {% endfor %}"
                                                ></i>
                                            {% else %}
                                                <i class="fas fa-file-alt"
                                                style="color: #ccc; cursor: pointer;"
                                                title="Aucun commentaire"
                                                data-bs-toggle="tooltip"
                                                data-document-id="{{ document.id }}"
                                                onmouseenter="loadCommentsTooltip(this)"
                                                onclick="showCommentsModal({{ document.id }}, '{{ document.reference|escape('js') }}')"
                                                title="Cliquer pour voir les commentaires ({{ document.commentaires|length }}) | Survoler pour aperçu"
                                                txt="{% for comment in document.commentaires %}
                                                <strong>{{ comment.state|upper|e }}</strong> : {{ comment.commentaire|e }}
                                                par <em>{{ comment.user|e }}</em><br>
                                                {% endfor %}"
                                                ></i>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            {% if document.CurrentStepsVisa|length > 1 %}
                                                <div class="dropdown">
                                                    <span id="dropdownMenuButton{{ document.id }}"
                                                          data-bs-toggle="dropdown"
                                                          aria-expanded="false"
                                                          class="badge bg-primary">
                                                          {{ document.CurrentStepsVisa|length }}
                                                    </span>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ document.id }}">
                                                        {% for step, value in document.CurrentStepsVisa %}
                                                            <li><a class="dropdown-item" href="{{ path('app_document_place', {'place': step}) }}">{{ step|replace({'_': ' '})|first|upper ~ step|replace({'_': ' '})|slice(1) }}</a></li>
                                                        {% endfor %}
                                                    </ul>
                                                </div>
                                            {% else %}
                                                {% for step, value in document.CurrentStepsVisa %}
                                                    <span class="badge bg-primary">{{ step }}</span>
                                                {% endfor %}
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="d-flex justify-content-center align-items-center">
                                                <div class="form-check">
                                                   <input class="form-check-input doc-select"
                                                    type="checkbox"
                                                    data-document-id="{{ document.id }}"
                                                    data-current-steps='{{ document.CurrentStepsVisa|json_encode|e('html_attr') }}'>

                                                </div>
                                                <span class="badge bg-primary" onclick='createVisa("{{ document.id|escape('js') }}")'>Signe</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary" onclick="showVisas({{ document.id }})"><i class="fa-solid fa-passport"></i></span>
                                        </td>
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div><!-- ./table-responsive -->
                    </div><!-- ./card-body -->
                </div><!-- ./card -->
            {% endif %}
            {# Pagination supprimée #}
        </div><!-- ./col -->
    </div><!-- ./row -->
</div><!-- ./container -->

<div class="modal fade" id="modalVisas" tabindex="-1" aria-labelledby="modalVisasLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-lg">
        <div class="modal-content custom-modal-content">
            <div class="modal-header custom-modal-header">
                <h5 class="modal-title" id="modalVisasLabel">Historique des visas</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                        aria-label="Close"></button>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>

<!-- Modal pour les commentaires -->
<div class="modal fade" id="modalComments" tabindex="-1" aria-labelledby="modalCommentsLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-lg">
        <div class="modal-content custom-modal-content">
            <div class="modal-header custom-modal-header">
                <h5 class="modal-title" id="modalCommentsLabel">
                    <i class="fas fa-comments me-2"></i>
                    Commentaires - <span id="documentReference"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="commentsModalBody">
                <!-- Les commentaires seront chargés ici -->
            </div>
            <div class="modal-footer custom-modal-footer">
                <div class="w-100">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <span class="badge bg-primary" style="background-color: #009BFF !important; font-size: 0.9rem; padding: 8px 12px;">
                                {{ place|replace({'_': ' '})|title }}
                            </span>
                        </div>
                        <div class="col-md-8">
                            <textarea class="form-control form-control-sm"
                                      id="newCommentText"
                                      placeholder="Ajouter un commentaire pour {{ place|replace({'_': ' '})|title }}..."
                                      rows="2"></textarea>
                        </div>
                        <div class="col-md-2">
                            <button type="button"
                                    class="btn btn-primary btn-sm w-100"
                                    id="addCommentBtn"
                                    onclick="addNewComment()">
                                <i class="fas fa-plus me-1"></i>
                                Ajouter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{% include 'js/jhess.html.twig' %}

<script>
    // Fonction pour sélectionner tous les documents à travers toutes les pages
    function selectAllDocumentsAcrossPages() {
        if (window.allDocumentsSelected) {
            // Désélectionner tous les documents
            $('.doc-select').prop('checked', false);
            $('.doc-select').closest('tr').find('td').removeClass('selected-row');
            $(document).find('#signer-mass').hide();
            $(document).find('#select-all-pages').text('Sélectionner tous');
            $(document).find('#select-all-pages').removeClass('bg-warning').addClass('bg-info');
            window.allDocumentsSelected = false;
        } else {
            // Récupérer tous les IDs de documents pour cette place
            const place = window.location.pathname.split('/').pop();
            window.showLoading();

            $.ajax({
                url: "{{ path('app_document_get_all_ids', {'place': 'PLACE_PLACEHOLDER'}) }}".replace('PLACE_PLACEHOLDER', place),
                type: 'GET',
                success: function(data) {
                    window.hideLoading();
                    window.allDocumentsAcrossPages = data;

                    // Sélectionner tous les documents visibles sur la page actuelle
                    $('.doc-select').prop('checked', true);
                    $('.doc-select').closest('tr').find('td').addClass('selected-row');

                    // Mettre à jour le bouton de signature en masse
                    $(document).find('#signer-mass').show();
                    $(document).find('#signer-mass').text(`Signer tous (${window.allDocumentsAcrossPages.length})`);

                    // Mettre à jour le bouton de sélection
                    $(document).find('#select-all-pages').text('Désélectionner tous');
                    $(document).find('#select-all-pages').removeClass('bg-info').addClass('bg-warning');

                    window.allDocumentsSelected = true;

                    Toast.fire({
                        icon: 'success',
                        title: `${window.allDocumentsAcrossPages.length} documents sélectionnés à travers toutes les pages`
                    });
                },
                error: function(error) {
                    window.hideLoading();
                    console.error('Erreur lors de la récupération des IDs de documents:', error);
                    Toast.fire({
                        icon: 'error',
                        title: 'Erreur lors de la récupération des documents'
                    });
                }
            });
        }
    }

    // Sauvegarde de la fonction originale
    const originalSignSelectedDocuments = window.signSelectedDocuments;

    // Simplifier la fonction signSelectedDocuments - utiliser toujours la méthode qui fonctionne
    window.signSelectedDocuments = function() {
        // Récupérer les checkboxes réellement cochées
        const checkedBoxes = document.querySelectorAll('.doc-select:checked');

        if (checkedBoxes.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'Aucune sélection',
                text: 'Veuillez cocher au moins un document à signer.'
            });
            return;
        }

        // Déterminer le message selon le contexte
        let confirmText = `Vous êtes sur le point de signer ${checkedBoxes.length} document(s) sélectionné(s).`;
        if (window.allDocumentsSelected && window.allDocumentsAcrossPages) {
            confirmText = `Vous êtes sur le point de signer ${window.allDocumentsAcrossPages.length} documents à travers toutes les pages.`;
        }

        // Confirmation pour la signature des documents
        Swal.fire({
            title: 'Êtes-vous sûr ?',
            text: confirmText,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, signer !',
            cancelButtonText: 'Annuler',
            customClass: {
                confirmButton: 'btn btn-sm btn-success',
                cancelButton: 'btn btn-sm btn-danger'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Afficher un indicateur de progression personnalisé
                showProgressModal(checkedBoxes.length);

                const totalCount = checkedBoxes.length;
                let successCount = 0;
                let errorCount = 0;
                let completedCount = 0;

                // Utiliser toujours la méthode parallélisée qui fonctionne
                const promises = Array.from(checkedBoxes).map(checkbox => {
                    const docId = checkbox.dataset.documentId;
                    const data = {
                        documentId: docId,
                        name: window.location.pathname.split('/').pop()
                    };

                    return $.ajax({
                        url: "{{ path('create_visa') }}",
                        type: "POST",
                        contentType: "application/json",
                        data: JSON.stringify(data)
                    }).then(
                        function(result) {
                            completedCount++;
                            if (result.status === "success") {
                                successCount++;
                            } else {
                                errorCount++;
                            }
                            updateProgress(completedCount, totalCount, successCount, errorCount);
                            return { success: true, result: result };
                        },
                        function(xhr) {
                            completedCount++;
                            errorCount++;
                            updateProgress(completedCount, totalCount, successCount, errorCount);
                            return { success: false, error: xhr };
                        }
                    );
                });

                // Attendre que toutes les promesses soient résolues
                Promise.allSettled(promises).then(() => {
                    setTimeout(() => {
                        hideProgressModal();
                        Toast.fire({
                            icon: successCount > 0 ? 'success' : 'error',
                            title: `Traitement terminé: ${successCount} documents signés, ${errorCount} erreurs`
                        });
                        window.refreshTable();
                    }, 500);
                });
            }
        });
    };

    // Fonctions pour l'indicateur de progression
    function showProgressModal(totalCount) {
        const modalHtml = `
            <div id="progressModal" class="modal fade" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-signature me-2"></i>
                                Signature en cours...
                            </h5>
                        </div>
                        <div class="modal-body text-center">
                            <div class="mb-3">
                                <div class="progress" style="height: 25px;">
                                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated bg-success"
                                         role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                        0%
                                    </div>
                                </div>
                            </div>
                            <div id="progressText" class="mb-2">
                                <strong>Progression:</strong> 0 / ${totalCount} documents traités
                            </div>
                            <div id="progressDetails" class="text-muted">
                                <small>
                                    <span class="text-success"><i class="fas fa-check"></i> Réussis: <span id="successCount">0</span></span> |
                                    <span class="text-danger"><i class="fas fa-times"></i> Erreurs: <span id="errorCount">0</span></span>
                                </small>
                            </div>
                            <div class="mt-3">
                                <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                                <small class="text-muted">Veuillez patienter...</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Supprimer le modal existant s'il y en a un
        $('#progressModal').remove();

        // Ajouter le nouveau modal au DOM
        $('body').append(modalHtml);

        // Afficher le modal
        $('#progressModal').modal('show');
    }

    function updateProgress(completed, total, successCount, errorCount) {
        const percentage = Math.round((completed / total) * 100);

        $('#progressBar').css('width', percentage + '%')
                        .attr('aria-valuenow', percentage)
                        .text(percentage + '%');

        $('#progressText').html(`<strong>Progression:</strong> ${completed} / ${total} documents traités`);
        $('#successCount').text(successCount);
        $('#errorCount').text(errorCount);

        // Changer la couleur de la barre selon les résultats
        if (errorCount > 0 && successCount === 0) {
            $('#progressBar').removeClass('bg-success').addClass('bg-danger');
        } else if (errorCount > 0) {
            $('#progressBar').removeClass('bg-success').addClass('bg-warning');
        }
    }

    function hideProgressModal() {
        $('#progressModal').modal('hide');
        setTimeout(() => {
            $('#progressModal').remove();
        }, 300);
    }

    // Gestionnaire d'événements pour détecter les changements de checkboxes
    $(document).on('change', '.doc-select', function() {
        if (window.allDocumentsSelected) {
            const checkedBoxes = document.querySelectorAll('.doc-select:checked');
            const totalVisibleBoxes = document.querySelectorAll('.doc-select').length;

            // Si le nombre de checkboxes cochées est inférieur au total visible,
            // cela signifie que l'utilisateur a décoché quelque chose
            if (checkedBoxes.length < totalVisibleBoxes) {
                // Réinitialiser le mode "sélectionner tous"
                window.allDocumentsSelected = false;
                $(document).find('#select-all-pages').text('Sélectionner tous');
                $(document).find('#select-all-pages').removeClass('bg-warning').addClass('bg-info');

                // Mettre à jour le bouton de signature en masse
                if (checkedBoxes.length > 0) {
                    $(document).find('#signer-mass').show();
                    $(document).find('#signer-mass').text(`Signer sélectionnés (${checkedBoxes.length})`);
                } else {
                    $(document).find('#signer-mass').hide();
                }
            }
        } else {
            // Mode normal : mettre à jour le bouton de signature en masse
            const checkedBoxes = document.querySelectorAll('.doc-select:checked');
            if (checkedBoxes.length > 0) {
                $(document).find('#signer-mass').show();
                $(document).find('#signer-mass').text(`Signer sélectionnés (${checkedBoxes.length})`);
            } else {
                $(document).find('#signer-mass').hide();
            }
        }
    });

    // Fonction pour afficher le modal des commentaires
    function showCommentsModal(documentId, documentReference) {
        // Stocker l'ID du document pour l'ajout de commentaires
        currentDocumentId = documentId;

        // Mettre à jour le titre du modal
        document.getElementById('documentReference').textContent = documentReference;

        // Afficher le modal
        const modal = new bootstrap.Modal(document.getElementById('modalComments'));
        modal.show();

        // Charger les commentaires
        loadCommentsForModal(documentId);
    }


    // Variable globale pour stocker l'ID du document actuel
    let currentDocumentId = null;

    // Fonction pour ajouter un nouveau commentaire (réutilise la logique de jhess)
    function addNewComment() {
        const commentTextarea = document.getElementById('newCommentText');
        const commentText = commentTextarea.value.trim();

        if (!commentText) {
            Toast.fire({
                icon: 'warning',
                title: 'Veuillez saisir un commentaire'
            });
            return;
        }

        if (!currentDocumentId) {
            Toast.fire({
                icon: 'error',
                title: 'Erreur: Document non identifié'
            });
            return;
        }

        // Désactiver le formulaire pendant l'envoi
        const form = document.querySelector('.modal-footer');
        form.classList.add('comment-form-loading');
        document.getElementById('addCommentBtn').innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Ajout...';

        // Utiliser la fonction addComment existante de jhess
        // Créer un élément temporaire pour simuler l'input du tableau
        const tempInput = $('<input>').val(commentText).data('document-id', currentDocumentId);

        // Sauvegarder les fonctions originales de jhess
        const originalShowLoading = window.showLoading;
        const originalHideLoading = window.hideLoading;
        const originalRefreshTable = window.refreshTable;

        // Remplacer temporairement les fonctions pour le modal
        window.showLoading = function() { /* déjà géré par le formulaire */ };
        window.hideLoading = function() {
            form.classList.remove('comment-form-loading');
            document.getElementById('addCommentBtn').innerHTML = '<i class="fas fa-plus me-1"></i>Ajouter';

            // Vider le champ de commentaire
            commentTextarea.value = '';

            // Recharger les commentaires dans le modal après succès
            setTimeout(() => {
                loadCommentsForModal(currentDocumentId);
            }, 200);
        };
        window.refreshTable = function() {
            // Ne rien faire pour éviter de rafraîchir le tableau
        };

        // Appeler la fonction addComment de jhess
        addComment(currentDocumentId, tempInput[0]);

        // Restaurer les fonctions originales après un délai
        setTimeout(() => {
            window.showLoading = originalShowLoading;
            window.hideLoading = originalHideLoading;
            window.refreshTable = originalRefreshTable;
        }, 1000);
    }

    // Fonction séparée pour charger les commentaires (réutilisable)
    function loadCommentsForModal(documentId) {
        document.getElementById('commentsModalBody').innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <p class="mt-2 text-muted">Chargement des commentaires...</p>
            </div>
        `;

        $.ajax({
            url: "{{ path('app_commentaire_get', {'documentId': 'DOCUMENT_ID_PLACEHOLDER'}) }}".replace('DOCUMENT_ID_PLACEHOLDER', documentId),
            type: 'GET',
            success: function(comments) {
                displayComments(comments);
            },
            error: function(xhr, status, error) {
                document.getElementById('commentsModalBody').innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Erreur lors du chargement des commentaires: ${error}
                    </div>
                `;
            }
        });
    }

    // Rendre les fonctions globales pour qu'elles soient accessibles depuis le HTML
    window.showCommentsModal = showCommentsModal;
    window.addNewComment = addNewComment;
</script>
{% endblock %}

