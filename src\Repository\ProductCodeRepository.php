<?php

namespace App\Repository;

use App\Entity\ProductCode;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ProductCode>
 */
class ProductCodeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ProductCode::class);
    }

    /**
     * Trouve un ProductCode par son code
     */
    public function findByCode(string $code): ?ProductCode
    {
        return $this->findOneBy(['code' => $code]);
    }

    /**
     * Trouve ou crée un ProductCode par son code
     */
    public function findOrCreateByCode(string $code, bool $etat = false): ProductCode
    {
        $productCode = $this->findByCode($code);
        
        if (!$productCode) {
            $productCode = new ProductCode();
            $productCode->setCode($code);
            $productCode->setEtat($etat);
            
            $this->getEntityManager()->persist($productCode);
            $this->getEntityManager()->flush();
        }
        
        return $productCode;
    }

    /**
     * Trouve tous les ProductCode actifs
     */
    public function findActive(): array
    {
        return $this->findBy(['etat' => true], ['code' => 'ASC']);
    }

    /**
     * Trouve tous les ProductCode inactifs
     */
    public function findInactive(): array
    {
        return $this->findBy(['etat' => false], ['code' => 'ASC']);
    }

    //    /**
    //     * @return ProductCode[] Returns an array of ProductCode objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('p.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?ProductCode
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
