<?php

namespace App\Command;

use App\Entity\ProductCode;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\DBAL\Attribute\Connection as ConnectionName;

#[AsCommand(
    name: 'app:migrate-product-code',
    description: 'Migre les ProductCode depuis db_scm.tbl_product_code'
)]
class MigrateProductCodeCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $em
    ) {
        parent::__construct();
    }

    /**
     * Créer une connexion à la base SCM
     */
    private function createScmConnection(): Connection
    {
        $connectionParams = [
            'dbname' => $_ENV['LEGACY_SCM_DB_NAME'] ?? 'db_scm',
            'user' => $_ENV['LEGACY_SCM_DB_USER'] ?? 'root',
            'password' => $_ENV['LEGACY_SCM_DB_PASSWORD'] ?? 'Lemans72!',
            'host' => $_ENV['LEGACY_SCM_DB_HOST'] ?? '************',
            'port' => $_ENV['LEGACY_SCM_DB_PORT'] ?? 3306,
            'driver' => 'pdo_mysql',
            'charset' => 'utf8mb4',
        ];

        return \Doctrine\DBAL\DriverManager::getConnection($connectionParams);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Début de la migration des ProductCode...</info>');

        try {
            // Vider la table product_code en gérant les contraintes de clé étrangère
            $output->writeln('<comment>Suppression des ProductCode existants...</comment>');

            // D'abord, mettre à NULL toutes les références dans la table document
            $this->em->getConnection()->executeStatement('UPDATE document SET product_code_id = NULL WHERE product_code_id IS NOT NULL');

            // Ensuite, supprimer tous les ProductCode
            $this->em->getConnection()->executeStatement('DELETE FROM product_code');
            $this->em->getConnection()->executeStatement('ALTER TABLE product_code AUTO_INCREMENT = 1');

            // Récupérer les données depuis db_scm.tbl_product_code
            $output->writeln('<comment>Récupération des données depuis db_scm.tbl_product_code...</comment>');

            $scmConnection = $this->createScmConnection();

            $productCodes = $scmConnection->fetchAllAssociative('
                SELECT
                    ID,
                    Code,
                    Description
                FROM tbl_product_code
                ORDER BY ID
            ');

            if (empty($productCodes)) {
                $output->writeln('<error>Aucun ProductCode trouvé dans db_scm.tbl_product_code !</error>');
                return Command::FAILURE;
            }

            $output->writeln('<info>ProductCode trouvés: ' . count($productCodes) . '</info>');

            $batchSize = 100;
            $count = 0;
            $batches = array_chunk($productCodes, $batchSize);

            foreach ($batches as $batchIndex => $batch) {
                $output->writeln('<comment>Traitement du lot ' . ($batchIndex + 1) . '/' . count($batches) . '...</comment>');

                foreach ($batch as $data) {
                    try {
                        $productCode = new ProductCode();
                        $productCode->setCode($data['Code'] ?? '');
                        $productCode->setDescription($data['Description'] ?? null);
                        $productCode->setEtat(true); // Actif par défaut pour les codes existants

                        $this->em->persist($productCode);
                        $count++;

                        if ($count % $batchSize === 0) {
                            $this->em->flush();
                            $this->em->clear();
                            $output->writeln('<comment>  → ' . $count . ' ProductCode migrés...</comment>');
                        }

                    } catch (\Exception $e) {
                        $output->writeln('<error>Erreur pour ProductCode ID ' . $data['ID'] . ': ' . $e->getMessage() . '</error>');
                    }
                }

                // Flush final pour le lot
                $this->em->flush();
                $this->em->clear();
            }

            $output->writeln('<info>Migration terminée ! ' . $count . ' ProductCode migrés avec succès.</info>');

            // Fermer la connexion SCM
            $scmConnection->close();

        } catch (\Exception $e) {
            $output->writeln('<error>Erreur lors de la migration: ' . $e->getMessage() . '</error>');
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
