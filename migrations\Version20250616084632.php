<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250616084632 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE document_materials (document_id INT NOT NULL, material_id INT NOT NULL, INDEX IDX_892F3E14C33F7837 (document_id), INDEX IDX_892F3E14E308AC6F (material_id), PRIMARY KEY(document_id, material_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE document_materials ADD CONSTRAINT FK_892F3E14C33F7837 FOREIGN KEY (document_id) REFERENCES document (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE document_materials ADD CONSTRAINT FK_892F3E14E308AC6F FOREIGN KEY (material_id) REFERENCES material (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE document_materials DROP FOREIGN KEY FK_892F3E14C33F7837');
        $this->addSql('ALTER TABLE document_materials DROP FOREIGN KEY FK_892F3E14E308AC6F');
        $this->addSql('DROP TABLE document_materials');
    }
}
