<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Migration pour supprimer l'ancien champ material (string) après migration vers Material entity
 */
final class Version20250616081500 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Supprime l\'ancien champ material (string) après migration vers Material entity';
    }

    public function up(Schema $schema): void
    {
        // Supprimer l'ancien champ material (string)
        $this->addSql('ALTER TABLE document DROP material');
    }

    public function down(Schema $schema): void
    {
        // Restaurer l'ancien champ material (string) si nécessaire
        $this->addSql('ALTER TABLE document ADD material VARCHAR(255) DEFAULT NULL');
    }
}
