<?php
// Script pour repeupler la base de données avec un nombre suffisant de documents par place

require_once __DIR__ . '/vendor/autoload.php';

use App\Entity\Document;
use App\Entity\ReleasedPackage;
use App\Entity\User;
use App\Entity\Visa;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

// Classe pour exécuter le script
class DatabasePopulator
{
    private EntityManagerInterface $entityManager;
    private ContainerInterface $container;
    private array $places = [
        'BE_0', 'BE_1', 'BE', 'Produit', 'Qual_Logistique', 'Logistique', 'Metro', 'Quality',
        'Achat_Rfq', 'Achat_RoHs_REACH', 'Assembly', 'Machining', 'Molding', 'Methode_assemblage',
        'Planning', 'Core_Data', 'Project', 'Achat_F30', 'Prod_Data', 'Achat_FIA', 'Achat_Hts',
        'Saisie_hts', 'Costing', 'GID', 'Indus', 'methode_Labo', 'QProd', 'Tirage_Plans'
    ];
    private array $docTypes = ['ASSY', 'MACH', 'MOLD', 'DOC', 'PUR'];
    private User $adminUser;
    private array $releasedPackages = [];

    public function __construct(KernelInterface $kernel)
    {
        $this->container = $kernel->getContainer();
        $this->entityManager = $this->container->get('doctrine')->getManager();
    }

    public function execute(): void
    {
        echo "Début du processus de repeuplement de la base de données...\n";

        // Étape 1: Vider les tables existantes
        $this->clearDatabase();

        // Étape 2: Créer un utilisateur administrateur si nécessaire
        $this->createAdminUser();

        // Étape 3: Créer plusieurs ReleasedPackage
        $this->createReleasedPackages();

        // Étape 4: Pour chaque place, créer plusieurs documents
        $this->createDocumentsForAllPlaces();

        echo "Repeuplement de la base de données terminé avec succès!\n";
    }

    private function clearDatabase(): void
    {
        echo "Suppression des données existantes...\n";

        // Supprimer d'abord les commentaires (dépendance)
        echo "Suppression des commentaires...\n";
        $this->entityManager->createQuery('DELETE FROM App\Entity\Commentaire')->execute();

        // Supprimer les visas (dépendance)
        echo "Suppression des visas...\n";
        $this->entityManager->createQuery('DELETE FROM App\Entity\Visa')->execute();

        // Supprimer les documents
        echo "Suppression des documents...\n";
        $this->entityManager->createQuery('DELETE FROM App\Entity\Document')->execute();

        // Supprimer les packages
        echo "Suppression des packages...\n";
        $this->entityManager->createQuery('DELETE FROM App\Entity\ReleasedPackage')->execute();

        $this->entityManager->flush();
        echo "Données existantes supprimées.\n";
    }

    private function createAdminUser(): void
    {
        echo "Vérification de l'existence d'un utilisateur administrateur...\n";

        $userRepository = $this->entityManager->getRepository(User::class);
        $adminUser = $userRepository->findOneBy(['email' => '<EMAIL>']);

        if (!$adminUser) {
            echo "Création d'un utilisateur administrateur...\n";
            $adminUser = new User();
            $adminUser->setEmail('<EMAIL>');
            $adminUser->setUsername('admin');
            $adminUser->setNom('Admin');
            $adminUser->setPrenom('System');
            $adminUser->setRoles(['ROLE_ADMIN']);
            $adminUser->setPassword('$2y$13$hMzHnQULfS/FVsXZjn9qYuPSPKELxjLOY8NSyVmj.ZK2x8latYAYe'); // 'password'
            $adminUser->setDepartement('IT');

            $this->entityManager->persist($adminUser);
            $this->entityManager->flush();
            echo "Utilisateur administrateur créé.\n";
        } else {
            echo "Utilisateur administrateur existant trouvé.\n";
        }

        $this->adminUser = $adminUser;
    }

    private function createReleasedPackages(): void
    {
        echo "Création des packages...\n";

        for ($i = 1; $i <= 5; $i++) {
            $package = new ReleasedPackage();
            $package->setDescription("Package de test #$i");
            $package->setActivity("Activity $i");
            $package->setEx("EX$i");
            $package->setCreationDate(new \DateTime());
            $package->setOwner($this->adminUser);

            $this->entityManager->persist($package);
            $this->releasedPackages[] = $package;
        }

        $this->entityManager->flush();
        echo count($this->releasedPackages) . " packages créés.\n";
    }

    private function createDocumentsForAllPlaces(): void
    {
        echo "Création des documents pour chaque place...\n";

        foreach ($this->places as $place) {
            $this->createDocumentsForPlace($place, 20); // 20 documents par place
        }

        echo "Documents créés pour toutes les places.\n";
    }

    private function createDocumentsForPlace(string $place, int $count): void
    {
        echo "Création de $count documents pour la place '$place'...\n";

        for ($i = 1; $i <= $count; $i++) {
            $document = $this->createDocument($place, $i);
            $this->setDocumentPlace($document, $place);
            $this->createVisasForDocument($document, $place);

            if ($i % 10 === 0) {
                $this->entityManager->flush();
                echo "Progression: $i/$count documents créés pour '$place'.\n";
            }
        }

        $this->entityManager->flush();
        echo "$count documents créés pour la place '$place'.\n";
    }

    private function createDocument(string $place, int $index): Document
    {
        $document = new Document();
        $document->setReference("REF-$place-$index");
        $document->setRefRev("R" . rand(1, 9));
        $document->setRefTitleFra("Document de test pour $place #$index");

        // Définir ProdDraw en fonction des contraintes
        $prodDrawPrefix = 'DRAW';
        if ($place === 'Project') {
            // Pour Project, on a besoin de prodDraw commençant par GA ou FT
            $prodDrawPrefix = rand(0, 1) ? 'GA' : 'FT';
        }
        $document->setProdDraw("$prodDrawPrefix-$place-$index");
        $document->setProdDrawRev("D" . rand(1, 9));
        $document->setAlias("ALIAS-$place-$index");

        // Définir docType en fonction des contraintes de la place
        $docType = $this->getDocTypeForPlace($place);
        $document->setDocType($docType);

        // Définir inventoryImpact en fonction des contraintes
        $inventoryImpact = $this->getInventoryImpactForPlace($place, $docType);
        $document->setInventoryImpact($inventoryImpact);

        // Définir procType en fonction des contraintes
        $procType = $this->getProcTypeForPlace($place, $docType);
        $document->setProcType($procType);

        // Définir matProdType si nécessaire
        if ($place === 'Methode_assemblage') {
            $document->setMatProdType(rand(0, 1) ? 'HALB' : 'VERP');
        } else {
            $document->setMatProdType('HALB');
        }

        // Définir switchAletiq pour Tirage_Plans
        if ($place === 'Tirage_Plans') {
            $document->setSwitchAletiq(true);
        } else {
            $document->setSwitchAletiq(false);
        }

        // Définir d'autres champs communs
        $document->setRelPack($this->releasedPackages[array_rand($this->releasedPackages)]);
        $document->setDocImpact(true);
        $document->setWeight(rand(10, 1000));
        $document->setWeightUnit('g');
        $document->setPlatingSurface(rand(10, 100));
        $document->setPlatingSurfaceUnit('cm²');
        $document->setInternalMachRec(true);
        $document->setCls(rand(1, 5));
        $document->setMoq(rand(10, 100));
        $document->setProductCode("PC-$place-$index");
        $document->setProdAgent("PA-$place-$index");
        $document->setMof("MOF-$place-$index");
        $document->setCommodityCode("CC-$place-$index");
        $document->setPurchasingGroup("PG-$place-$index");
        $document->setMatProdType("MPT-$place-$index");
        $document->setUnit('EA');
        $document->setLeadtime(rand(5, 30));
        $document->setPrisDans1("PD1-$place-$index");
        $document->setPrisDans2("PD2-$place-$index");
        $document->setEccn("ECCN-$place-$index");
        $document->setRdo("RDO-$place-$index");
        $document->setHts("HTS-$place-$index");
        $document->setFia("FIA-$place-$index");
        $document->setMetroTime(rand(10, 60));
        $document->setQInspection(['Inspection1', 'Inspection2']);
        $document->setQDynamization("QD-$place-$index");
        $document->setQDocRec(['DocRec1', 'DocRec2']);
        $document->setQControlRouting("QCR-$place-$index");
        $document->setCriticalComplete(rand(0, 1));
        $document->setMaterial("MAT-$place-$index");
        $document->setMetroControl(['Control1', 'Control2']);
        $document->setSuperviseur($this->adminUser);

        // Ajouter un timestamp pour la place actuelle
        $stateTimestamps = [];
        $stateTimestamps[$place] = (new \DateTime())->format('Y-m-d H:i:s');
        $document->setStateTimestamps($stateTimestamps);

        $this->entityManager->persist($document);

        return $document;
    }

    private function getDocTypeForPlace(string $place): string
    {
        switch ($place) {
            case 'Quality':
                return ['PUR', 'ASSY', 'DOC', 'MOLD'][array_rand(['PUR', 'ASSY', 'DOC', 'MOLD'])];
            case 'Qual_Logistique':
            case 'Logistique':
                return ['PUR', 'ASSY', 'MACH', 'MOLD'][array_rand(['PUR', 'ASSY', 'MACH', 'MOLD'])];
            case 'Assembly':
            case 'Methode_assemblage':
                return ['ASSY', 'DOC'][array_rand(['ASSY', 'DOC'])];
            case 'Machining':
                return 'MACH';
            case 'Molding':
                return 'MOLD';
            case 'Achat_Rfq':
            case 'Achat_F30':
            case 'Achat_FIA':
            case 'Achat_Hts':
                return 'PUR';
            case 'Indus':
            case 'methode_Labo':
                return 'ASSY';
            case 'QProd':
                return ['MACH', 'MOLD'][array_rand(['MACH', 'MOLD'])];
            case 'Tirage_Plans':
                return ['MACH', 'MOLD'][array_rand(['MACH', 'MOLD'])];
            default:
                return $this->docTypes[array_rand($this->docTypes)];
        }
    }

    private function getInventoryImpactForPlace(string $place, string $docType): string
    {
        if ($place === 'Qual_Logistique' || $place === 'Logistique') {
            return ['Yes', 'TO BE UPDATED', 'TO BE SCRAPPED'][array_rand(['Yes', 'TO BE UPDATED', 'TO BE SCRAPPED'])];
        } elseif ($docType === 'DOC') {
            return 'NO IMPACT';
        } else {
            return ['Yes', 'TO BE UPDATED', 'TO BE SCRAPPED', 'NO IMPACT'][array_rand(['Yes', 'TO BE UPDATED', 'TO BE SCRAPPED', 'NO IMPACT'])];
        }
    }

    private function getProcTypeForPlace(string $place, string $docType): string
    {
        switch ($place) {
            case 'Assembly':
            case 'Machining':
            case 'Molding':
                return 'E';
            case 'Achat_F30':
                return 'F30';
            case 'Achat_FIA':
            case 'Achat_Hts':
                return ['F', 'F30'][array_rand(['F', 'F30'])];
            case 'Achat_Rfq':
                if ($docType === 'PUR') {
                    return ['F', 'F30'][array_rand(['F', 'F30'])];
                }
                break;
            default:
                break;
        }

        // Valeur par défaut
        return ['A', 'B', 'C', 'D', 'E', 'F', 'G'][array_rand(['A', 'B', 'C', 'D', 'E', 'F', 'G'])];
    }

    private function setDocumentPlace(Document $document, string $place): void
    {
        // Définir currentSteps pour la place spécifiée
        $currentSteps = [$place => 1];
        $document->setCurrentSteps($currentSteps);
    }

    private function createVisasForDocument(Document $document, string $place): void
    {
        // Déterminer les visas nécessaires en fonction de la place
        $requiredVisas = $this->getRequiredVisasForPlace($place);

        foreach ($requiredVisas as $visaName) {
            $visa = new Visa();
            $visa->setReleasedDrawing($document);
            $visa->setName($visaName);
            $visa->setStatus('valid');
            $visa->setDateVisa(new \DateTimeImmutable());
            $visa->setValidator($this->adminUser);

            $this->entityManager->persist($visa);
        }
    }

    private function getRequiredVisasForPlace(string $place): array
    {
        // Logique pour déterminer les visas nécessaires en fonction de la place
        $visas = [];

        // Ajouter les visas des étapes précédentes selon le workflow
        switch ($place) {
            case 'BE_0':
                // Aucun visa requis pour BE_0
                break;
            case 'BE_1':
                $visas[] = 'visa_BE_0';
                break;
            case 'BE':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                break;
            case 'Produit':
            case 'Qual_Logistique':
            case 'Quality':
            case 'Project':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                break;
            case 'Logistique':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Qual_Logistique';
                break;
            case 'Assembly':
            case 'Machining':
            case 'Molding':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Produit';
                break;
            case 'Achat_Rfq':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Produit';
                break;
            case 'Achat_RoHs_REACH':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Produit';
                $visas[] = 'visa_Achat_Rfq';
                break;
            case 'Methode_assemblage':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Produit';
                $visas[] = 'visa_Assembly';
                break;
            case 'Planning':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Produit';
                $visas[] = 'visa_Assembly';
                break;
            case 'Metro':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Quality';
                break;
            case 'Core_Data':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Quality';
                break;
            case 'Achat_F30':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Produit';
                $visas[] = 'visa_Achat_Rfq';
                break;
            case 'Prod_Data':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Core_Data';
                break;
            case 'Achat_FIA':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Core_Data';
                $visas[] = 'visa_Prod_Data';
                break;
            case 'Achat_Hts':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Core_Data';
                $visas[] = 'visa_Prod_Data';
                $visas[] = 'visa_Achat_FIA';
                break;
            case 'Saisie_hts':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Core_Data';
                $visas[] = 'visa_Prod_Data';
                $visas[] = 'visa_Achat_FIA';
                $visas[] = 'visa_Achat_Hts';
                break;
            case 'Costing':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Core_Data';
                $visas[] = 'visa_Prod_Data';
                $visas[] = 'visa_Achat_FIA';
                break;
            case 'GID':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Metro';
                $visas[] = 'visa_Indus';
                break;
            case 'Indus':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Metro';
                break;
            case 'methode_Labo':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Metro';
                $visas[] = 'visa_Indus';
                break;
            case 'QProd':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Metro';
                break;
            case 'Tirage_Plans':
                $visas[] = 'visa_BE_0';
                $visas[] = 'visa_BE_1';
                $visas[] = 'visa_BE';
                $visas[] = 'visa_Core_Data';
                break;
            default:
                // Par défaut, ajouter au moins le visa de la place actuelle
                break;
        }

        // IMPORTANT: Ne pas ajouter le visa de la place actuelle
        // car le contrôleur filtre les documents qui ont déjà un visa pour la place actuelle

        return array_unique($visas);
    }
}

// Exécuter le script
$kernel = new \App\Kernel('dev', true);
$kernel->boot();

$populator = new DatabasePopulator($kernel);
$populator->execute();
