<?php

namespace App\Command;

use App\Entity\Material;
use App\Entity\Document;
use App\Repository\MaterialRepository;
use App\Repository\DocumentRepository;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\DBAL\Attribute\Connection as ConnectionName;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:migrate-materials',
    description: 'Migre les matériaux de l\'ancienne base de données et met à jour les relations'
)]
class MigrateMaterialsCommand extends Command
{
    private array $materialCache = [];

    public function __construct(
        #[ConnectionName('legacy_scm')]
        private Connection $scmDb,
        private EntityManagerInterface $em,
        private MaterialRepository $materialRepository,
        private DocumentRepository $documentRepository
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('dry-run', null, InputOption::VALUE_NONE, 'Exécute en mode test sans sauvegarder')
            ->addOption('force', null, InputOption::VALUE_NONE, 'Force la réimportation même si des matériaux existent déjà');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $dryRun = $input->getOption('dry-run');
        $force = $input->getOption('force');

        $io->title('Migration des matériaux');

        if ($dryRun) {
            $io->note('Mode DRY-RUN activé - aucune modification ne sera sauvegardée');
        }

        try {
            $io->writeln('Test de connexion SCM...');
            $stmt = $this->scmDb->executeQuery('SELECT COUNT(*) as total FROM tbl_fxxx');
            $result = $stmt->fetchAssociative();
            $io->writeln('Connexion OK - ' . $result['total'] . ' matériaux trouvés');

            // Étape 1: Importer les matériaux depuis l'ancienne base
            $this->importMaterials($io, $dryRun, $force);

            // Étape 2: Migrer vers le système de matériaux multiples
            $this->migrateToMultipleMaterials($io, $dryRun);

            $io->success('Migration des matériaux terminée avec succès !');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $io->error('Erreur lors de la migration : ' . $e->getMessage());
            $io->writeln('Stack trace: ' . $e->getTraceAsString());
            return Command::FAILURE;
        }
    }

    private function importMaterials(SymfonyStyle $io, bool $dryRun, bool $force): void
    {
        $io->section('Importation des matériaux depuis tbl_fxxx');

        // Vérifier si des matériaux existent déjà
        $existingCount = $this->materialRepository->count([]);
        if ($existingCount > 0 && !$force) {
            $io->warning("$existingCount matériaux existent déjà. Utilisez --force pour réimporter.");
            return;
        }

        if ($force && $existingCount > 0) {
            $io->note("Suppression des $existingCount matériaux existants...");
            if (!$dryRun) {
                $this->em->createQuery('DELETE FROM App\Entity\Material')->execute();
            }
        }

        // Récupérer les données de l'ancienne base
        $sql = 'SELECT * FROM tbl_fxxx ORDER BY ID';
        $stmt = $this->scmDb->executeQuery($sql);
        $materials = $stmt->fetchAllAssociative();

        $io->progressStart(count($materials));

        $batchSize = 100;
        $count = 0;

        foreach ($materials as $materialData) {
            $material = new Material();
            $material->setReference(trim($materialData['fxxx_ref']));
            $material->setDescription(trim($materialData['fxxx_description']));
            $material->setThicknessMin($materialData['Thickness_Min'] ?: null);
            $material->setThicknessMax($materialData['Thickness_Max'] ?: null);
            $material->setThicknessUnit(trim($materialData['Thickness_Unit']) ?: null);
            $material->setDensity($materialData['Density'] ?: null);
            $material->setStatus(trim($materialData['Status']));
            $material->setRohs(trim($materialData['fxxx_rohs']) ?: null);
            $material->setReach(trim($materialData['fxxx_reach']) ?: null);
            $material->setLegacyId($materialData['ID']);

            if (!$dryRun) {
                $this->em->persist($material);
            }

            // Cache pour la mise à jour des relations
            $this->materialCache[$materialData['fxxx_ref']] = $material;

            $count++;
            if ($count % $batchSize === 0 && !$dryRun) {
                $this->em->flush();
                $this->em->clear();

                // Recharger les entités après clear
                $this->reloadMaterialCache();
            }

            $io->progressAdvance();
        }

        if (!$dryRun) {
            $this->em->flush();
            $this->em->clear();
        }

        $io->progressFinish();
        $io->success("Importé " . count($materials) . " matériaux");
    }

    private function migrateToMultipleMaterials(SymfonyStyle $io, bool $dryRun): void
    {
        $io->section('Migration vers le système de matériaux multiples');

        // Compter le nombre total de documents à traiter
        $qb = $this->em->createQueryBuilder();
        $totalCount = $qb->select('COUNT(d.id)')
            ->from(Document::class, 'd')
            ->leftJoin('d.materials', 'm')
            ->where('d.materialEntity IS NOT NULL')
            ->andWhere('m.id IS NULL') // Pas de matériaux multiples
            ->getQuery()
            ->getSingleScalarResult();

        $io->progressStart($totalCount);

        $updated = 0;
        $batchSize = 100;
        $offset = 0;

        while ($offset < $totalCount) {
            // Récupérer un lot de documents
            $qb = $this->em->createQueryBuilder();
            $documents = $qb->select('d')
                ->from(Document::class, 'd')
                ->leftJoin('d.materials', 'm')
                ->where('d.materialEntity IS NOT NULL')
                ->andWhere('m.id IS NULL')
                ->setFirstResult($offset)
                ->setMaxResults($batchSize)
                ->getQuery()
                ->getResult();

            foreach ($documents as $document) {
                $materialEntity = $document->getMaterialEntity();

                if ($materialEntity && !$document->getMaterials()->contains($materialEntity)) {
                    if (!$dryRun) {
                        $document->addMaterial($materialEntity);
                        $this->em->persist($document);
                    }
                    $updated++;
                }

                $io->progressAdvance();
            }

            if (!$dryRun) {
                $this->em->flush();
                $this->em->clear();
            }

            $offset += $batchSize;
        }

        if (!$dryRun) {
            $this->em->flush();
        }

        $io->progressFinish();
        $io->success("Relations mises à jour: $updated documents migrés vers les matériaux multiples");
    }

    private function findMaterialByReference(string $reference): ?Material
    {
        // Chercher d'abord dans le cache
        if (isset($this->materialCache[$reference])) {
            return $this->materialCache[$reference];
        }

        // Chercher en base
        $material = $this->materialRepository->findByReference($reference);
        if ($material) {
            $this->materialCache[$reference] = $material;
        }

        return $material;
    }

    private function reloadMaterialCache(): void
    {
        $this->materialCache = [];
        $materials = $this->materialRepository->findAll();
        foreach ($materials as $material) {
            $this->materialCache[$material->getReference()] = $material;
        }
    }


}
